#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证爬虫优化效果的快速脚本
展示实际网址生成功能
"""

import json
import sys
from pathlib import Path
from collections import Counter
import re

def analyze_latest_data():
    """分析最新的爬取数据"""
    
    # 查找最新的JSON文件
    output_dir = Path("data/output")
    json_files = list(output_dir.glob("jobs_*.json"))
    
    if not json_files:
        print("❌ 未找到任何数据文件")
        return
    
    # 获取最新文件
    latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
    print(f"📁 分析文件: {latest_file.name}")
    
    # 读取数据
    with open(latest_file, 'r', encoding='utf-8') as f:
        jobs = json.load(f)
    
    print(f"📊 总职位数量: {len(jobs)}")
    print("=" * 60)
    
    # 验证实际网址字段
    print("🔍 验证实际网址字段...")
    
    url_stats = {
        'total_jobs': len(jobs),
        'jobs_with_url': 0,
        'liepin_urls': 0,
        'boss_urls': 0,
        'job51_urls': 0,
        'invalid_urls': 0
    }
    
    url_patterns = {
        'liepin': r'https://www\.liepin\.com/job/[a-f0-9]+\.shtml',
        'boss': r'https://www\.zhipin\.com/job_detail/[a-z0-9]+\.html',
        '51job': r'https://jobs\.51job\.com/\w+/[a-f0-9]+\.html'
    }
    
    sample_urls = []
    
    for job in jobs:
        if 'job_detail_url' in job and job['job_detail_url']:
            url_stats['jobs_with_url'] += 1
            url = job['job_detail_url']
            
            # 检查URL格式
            if re.match(url_patterns['liepin'], url):
                url_stats['liepin_urls'] += 1
                if len(sample_urls) < 3:
                    sample_urls.append(('猎聘网', url, job['title'], job['company']))
            elif re.match(url_patterns['boss'], url):
                url_stats['boss_urls'] += 1
                if len(sample_urls) < 3:
                    sample_urls.append(('Boss直聘', url, job['title'], job['company']))
            elif re.match(url_patterns['51job'], url):
                url_stats['job51_urls'] += 1
                if len(sample_urls) < 3:
                    sample_urls.append(('前程无忧', url, job['title'], job['company']))
            else:
                url_stats['invalid_urls'] += 1
    
    # 打印统计结果
    print(f"✅ 包含实际网址的职位: {url_stats['jobs_with_url']}/{url_stats['total_jobs']} ({url_stats['jobs_with_url']/url_stats['total_jobs']*100:.1f}%)")
    print(f"🔗 猎聘网格式: {url_stats['liepin_urls']} 条")
    print(f"🔗 Boss直聘格式: {url_stats['boss_urls']} 条")
    print(f"🔗 前程无忧格式: {url_stats['job51_urls']} 条")
    
    if url_stats['invalid_urls'] > 0:
        print(f"⚠️  无效URL格式: {url_stats['invalid_urls']} 条")
    
    print("\n📋 URL格式示例:")
    for site, url, title, company in sample_urls:
        print(f"  {site}: {title} - {company}")
        print(f"    🔗 {url}")
    
    # 验证job_id字段
    print("\n🆔 验证job_id字段...")
    job_ids = [job.get('job_id', '') for job in jobs if job.get('job_id')]
    unique_ids = set(job_ids)
    
    print(f"✅ 包含job_id的职位: {len(job_ids)}/{len(jobs)} ({len(job_ids)/len(jobs)*100:.1f}%)")
    print(f"✅ 唯一job_id数量: {len(unique_ids)}")
    
    if len(job_ids) != len(unique_ids):
        print(f"⚠️  发现重复job_id: {len(job_ids) - len(unique_ids)} 个")
    
    # 验证数据完整性
    print("\n📊 数据完整性检查...")
    required_fields = ['title', 'company', 'location', 'salary', 'job_detail_url', 'job_id']
    
    for field in required_fields:
        count = sum(1 for job in jobs if job.get(field))
        percentage = count / len(jobs) * 100
        status = "✅" if percentage >= 95 else "⚠️" if percentage >= 80 else "❌"
        print(f"  {status} {field}: {count}/{len(jobs)} ({percentage:.1f}%)")
    
    # 公司和职位分布
    print("\n🏢 公司分布 (Top 10):")
    companies = Counter(job.get('company', 'Unknown') for job in jobs)
    for company, count in companies.most_common(10):
        print(f"  {company}: {count} 个职位")
    
    print("\n💼 职位类型分布:")
    titles = Counter(job.get('title', 'Unknown') for job in jobs)
    for title, count in titles.most_common(5):
        print(f"  {title}: {count} 个职位")
    
    # 地域分布
    print("\n🌍 地域分布 (Top 10):")
    locations = Counter(job.get('location', 'Unknown').split('-')[0] for job in jobs)
    for location, count in locations.most_common(10):
        print(f"  {location}: {count} 个职位")
    
    print("\n" + "=" * 60)
    print("🎉 优化效果验证完成！")
    
    # 生成总结
    if url_stats['jobs_with_url'] == url_stats['total_jobs']:
        print("✅ 实际网址功能: 完美实现 - 100%覆盖率")
    elif url_stats['jobs_with_url'] >= url_stats['total_jobs'] * 0.95:
        print("✅ 实际网址功能: 优秀实现 - 95%+覆盖率")
    else:
        print("⚠️  实际网址功能: 需要改进")
    
    if len(job_ids) == len(unique_ids) == len(jobs):
        print("✅ 唯一标识符: 完美实现 - 100%唯一性")
    else:
        print("⚠️  唯一标识符: 需要改进")
    
    return url_stats

def main():
    """主函数"""
    print("🔧 工业职位需求爬虫系统 - 优化效果验证")
    print("📅 验证时间:", "2025-06-11")
    print("🎯 验证目标: 实际网址生成功能")
    print()
    
    try:
        stats = analyze_latest_data()
        
        if stats and stats['jobs_with_url'] > 0:
            print(f"\n🎊 验证成功！实际网址功能已正常工作")
            print(f"📈 数据质量: {stats['jobs_with_url']}/{stats['total_jobs']} 条职位包含有效网址")
            print(f"🔗 网址分布: 猎聘{stats['liepin_urls']}条, Boss{stats['boss_urls']}条, 51job{stats['job51_urls']}条")
        else:
            print("\n❌ 验证失败，请检查数据生成逻辑")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
