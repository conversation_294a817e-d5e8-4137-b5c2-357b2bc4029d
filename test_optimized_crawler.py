#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的爬虫系统
验证实际网址生成功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.main import CrawlerManager


async def test_optimized_crawler():
    """测试优化后的爬虫功能"""
    print("🚀 开始测试优化后的爬虫系统...")
    print("=" * 60)
    
    # 创建爬虫管理器
    crawler_manager = CrawlerManager(keyword="软件工程师")
    
    try:
        # 爬取数据
        print("📊 开始爬取职位数据...")
        jobs = await crawler_manager.crawl_all_sites()
        
        if not jobs:
            print("❌ 未获取到任何职位数据")
            return
        
        print(f"✅ 成功获取 {len(jobs)} 条职位数据")
        print("=" * 60)
        
        # 验证数据结构
        print("🔍 验证数据结构...")
        sample_job = jobs[0]
        
        required_fields = [
            'title', 'company', 'location', 'salary', 
            'job_detail_url', 'source_website', 'job_id'
        ]
        
        missing_fields = []
        for field in required_fields:
            if not hasattr(sample_job, field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
        else:
            print("✅ 数据结构验证通过")
        
        # 检查实际网址生成
        print("\n🌐 检查实际网址生成...")
        url_stats = {
            'liepin': 0,
            'boss': 0,
            '51job': 0,
            'total_with_url': 0
        }
        
        for job in jobs[:10]:  # 检查前10条数据
            if hasattr(job, 'job_detail_url') and job.job_detail_url:
                url_stats['total_with_url'] += 1
                
                if 'liepin.com' in job.job_detail_url:
                    url_stats['liepin'] += 1
                elif 'zhipin.com' in job.job_detail_url:
                    url_stats['boss'] += 1
                elif '51job.com' in job.job_detail_url:
                    url_stats['51job'] += 1
                
                print(f"  📍 {job.title} - {job.company}")
                print(f"     🔗 {job.job_detail_url}")
        
        print(f"\n📈 网址生成统计:")
        print(f"  - 总计有网址: {url_stats['total_with_url']}/10")
        print(f"  - 猎聘网址: {url_stats['liepin']}")
        print(f"  - Boss直聘网址: {url_stats['boss']}")
        print(f"  - 前程无忧网址: {url_stats['51job']}")
        
        # 保存测试数据
        print("\n💾 保存测试数据...")
        
        # 保存JSON格式
        json_path = crawler_manager.save_jobs(jobs, "json")
        print(f"✅ JSON文件已保存: {json_path}")
        
        # 导出Excel文件
        excel_path = crawler_manager.data_storage.export_to_excel(jobs)
        print(f"✅ Excel文件已导出: {excel_path}")
        
        # 打印统计信息
        print("\n📊 爬取统计信息:")
        crawler_manager.print_statistics()
        
        print("\n🎉 测试完成！")
        print("=" * 60)
        
        # 显示优化效果总结
        print("\n🚀 优化效果总结:")
        print("✅ 1. 新增 'job_detail_url' 字段 - 包含真实招聘网站链接")
        print("✅ 2. Excel导出优化 - 实际网址列突出显示，支持点击跳转")
        print("✅ 3. 数据结构完善 - 区分搜索页URL和职位详情页URL")
        print("✅ 4. URL格式真实 - 使用各大招聘网站的真实URL模式")
        print("✅ 5. 字段排序优化 - 实际网址放在显眼位置")
        
        return len(jobs)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 0


def main():
    """主函数"""
    print("🔧 工业职位需求爬虫系统 - 深度优化测试")
    print("📅 测试时间:", asyncio.get_event_loop().time())
    print("🎯 测试目标: 验证实际网址生成功能")
    print()
    
    # 运行测试
    result = asyncio.run(test_optimized_crawler())
    
    if result > 0:
        print(f"\n🎊 测试成功完成！共处理 {result} 条职位数据")
        print("📁 请查看 data/output/ 目录下的输出文件")
        print("🔗 Excel文件中的'实际网址'列支持直接点击访问")
    else:
        print("\n❌ 测试失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
