2025-06-11 09:55:15 - crawler - [32m<PERSON><PERSON><PERSON>[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 软件工程师
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 09:55:20 - crawler - [32mIN<PERSON>O[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 09:55:20 - crawler - [32mIN<PERSON><PERSON>[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 软件工程师
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 130
2025-06-11 09:55:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 130 条
2025-06-11 09:55:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 130 条职位
2025-06-11 09:55:23 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 09:55:23 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 103
2025-06-11 09:55:24 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 103 条
2025-06-11 09:55:24 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 103 条职位
2025-06-11 09:55:26 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 09:55:26 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 114
2025-06-11 09:55:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 114 条
2025-06-11 09:55:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 114 条职位
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 347 条职位数据
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 343 条有效职位数据
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 343 条职位
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 343 条职位，耗时 8.39 秒
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_095528.json
2025-06-11 09:55:29 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_095528.xlsx
2025-06-11 09:55:29 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 343 条职位数据
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 软件工程师
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 软件工程师
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 102
2025-06-11 09:58:51 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 102 条
2025-06-11 09:58:51 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 102 条职位
2025-06-11 09:58:52 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 09:58:52 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 111
2025-06-11 09:58:53 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 111 条
2025-06-11 09:58:53 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 111 条职位
2025-06-11 09:58:55 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 09:58:55 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 143
2025-06-11 09:58:57 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 143 条
2025-06-11 09:58:57 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 143 条职位
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 356 条职位数据
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 347 条有效职位数据
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 347 条职位
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 347 条职位，耗时 8.52 秒
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_095858.json
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_095858.xlsx
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 347 条职位数据
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: AI
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: AI
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=1
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 115
2025-06-11 10:00:52 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 115 条
2025-06-11 10:00:52 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 115 条职位
2025-06-11 10:00:53 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:00:53 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 135
2025-06-11 10:00:54 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 135 条
2025-06-11 10:00:54 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 135 条职位
2025-06-11 10:00:56 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:00:56 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 112
2025-06-11 10:00:57 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 112 条
2025-06-11 10:00:57 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 112 条职位
2025-06-11 10:00:58 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 362 条职位数据
2025-06-11 10:01:02 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:01:02 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=2
2025-06-11 10:01:02 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:01:02 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 132
2025-06-11 10:01:04 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 132 条
2025-06-11 10:01:04 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 132 条职位
2025-06-11 10:01:05 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:01:05 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 108
2025-06-11 10:01:07 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 108 条
2025-06-11 10:01:07 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 108 条职位
2025-06-11 10:01:08 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:01:08 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 126
2025-06-11 10:01:09 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 126 条
2025-06-11 10:01:09 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 126 条职位
2025-06-11 10:01:10 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 366 条职位数据
2025-06-11 10:01:14 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:01:14 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=3
2025-06-11 10:01:14 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:01:14 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 121
2025-06-11 10:01:15 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 121 条
2025-06-11 10:01:15 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 121 条职位
2025-06-11 10:01:17 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:01:17 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 124
2025-06-11 10:01:19 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 124 条
2025-06-11 10:01:19 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 124 条职位
2025-06-11 10:01:21 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:01:21 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 139
2025-06-11 10:01:22 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 139 条
2025-06-11 10:01:22 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 139 条职位
2025-06-11 10:01:24 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 384 条职位数据
2025-06-11 10:01:27 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:01:27 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=4
2025-06-11 10:01:27 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:01:27 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 144
2025-06-11 10:01:29 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 144 条
2025-06-11 10:01:29 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 144 条职位
2025-06-11 10:01:31 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:01:31 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 121
2025-06-11 10:01:32 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 121 条
2025-06-11 10:01:32 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 121 条职位
2025-06-11 10:01:34 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:01:34 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 109
2025-06-11 10:01:35 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 109 条
2025-06-11 10:01:35 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 109 条职位
2025-06-11 10:01:37 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 374 条职位数据
2025-06-11 10:01:39 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:01:39 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=5
2025-06-11 10:01:39 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:01:39 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 112
2025-06-11 10:01:40 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 112 条
2025-06-11 10:01:40 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 112 条职位
2025-06-11 10:01:42 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:01:42 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 143
2025-06-11 10:01:43 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 143 条
2025-06-11 10:01:43 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 143 条职位
2025-06-11 10:01:45 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:01:45 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 101
2025-06-11 10:01:46 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 101 条
2025-06-11 10:01:46 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 101 条职位
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 356 条职位数据
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1662 条有效职位数据
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1662 条职位
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1662 条职位，耗时 57.04 秒
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_100148.json
2025-06-11 10:01:49 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_100148.xlsx
2025-06-11 10:01:49 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 1662 条职位数据
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 通信工程
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 通信工程
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=1
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 106
2025-06-11 10:06:09 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 106 条
2025-06-11 10:06:09 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 106 条职位
2025-06-11 10:06:10 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:10 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 135
2025-06-11 10:06:12 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 135 条
2025-06-11 10:06:12 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 135 条职位
2025-06-11 10:06:13 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:06:13 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 127
2025-06-11 10:06:15 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 127 条
2025-06-11 10:06:15 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 127 条职位
2025-06-11 10:06:17 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 368 条职位数据
2025-06-11 10:06:20 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:06:20 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=2
2025-06-11 10:06:20 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:20 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 105
2025-06-11 10:06:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 105 条
2025-06-11 10:06:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 105 条职位
2025-06-11 10:06:23 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:23 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 129
2025-06-11 10:06:25 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 129 条
2025-06-11 10:06:25 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 129 条职位
2025-06-11 10:06:26 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:06:26 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 115
2025-06-11 10:06:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 115 条
2025-06-11 10:06:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 115 条职位
2025-06-11 10:06:28 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 349 条职位数据
2025-06-11 10:06:31 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:06:31 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=3
2025-06-11 10:06:31 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:31 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 121
2025-06-11 10:06:32 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 121 条
2025-06-11 10:06:32 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 121 条职位
2025-06-11 10:06:34 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:34 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 150
2025-06-11 10:06:35 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 150 条
2025-06-11 10:06:35 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 150 条职位
2025-06-11 10:06:36 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:06:36 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 112
2025-06-11 10:06:37 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 112 条
2025-06-11 10:06:37 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 112 条职位
2025-06-11 10:06:39 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 383 条职位数据
2025-06-11 10:06:42 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:06:42 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=4
2025-06-11 10:06:42 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:42 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 131
2025-06-11 10:06:44 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 131 条
2025-06-11 10:06:44 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 131 条职位
2025-06-11 10:06:45 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:45 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 148
2025-06-11 10:06:47 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 148 条
2025-06-11 10:06:47 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 148 条职位
2025-06-11 10:06:49 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:06:49 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 115
2025-06-11 10:06:50 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 115 条
2025-06-11 10:06:50 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 115 条职位
2025-06-11 10:06:52 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 394 条职位数据
2025-06-11 10:06:55 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:06:55 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=5
2025-06-11 10:06:55 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:55 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 113
2025-06-11 10:06:56 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 113 条
2025-06-11 10:06:56 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 113 条职位
2025-06-11 10:06:58 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:58 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 111
2025-06-11 10:06:59 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 111 条
2025-06-11 10:06:59 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 111 条职位
2025-06-11 10:07:01 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:07:01 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 127
2025-06-11 10:07:03 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 127 条
2025-06-11 10:07:03 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 127 条职位
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 351 条职位数据
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1669 条有效职位数据
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1669 条职位
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1669 条职位，耗时 57.22 秒
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_100705.json
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_100705.xlsx
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 1669 条职位数据
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 软件工程师
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 123
2025-06-11 10:17:59 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 123 条
2025-06-11 10:17:59 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 123 条职位
2025-06-11 10:18:00 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:00 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 142
2025-06-11 10:18:02 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 142 条
2025-06-11 10:18:02 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 142 条职位
2025-06-11 10:18:03 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:03 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 122
2025-06-11 10:18:04 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 122 条
2025-06-11 10:18:04 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 122 条职位
2025-06-11 10:18:06 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 387 条职位数据
2025-06-11 10:18:08 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:18:08 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=2
2025-06-11 10:18:08 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:18:08 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 141
2025-06-11 10:18:10 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 141 条
2025-06-11 10:18:10 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 141 条职位
2025-06-11 10:18:11 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:11 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 104
2025-06-11 10:18:12 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 104 条
2025-06-11 10:18:12 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 104 条职位
2025-06-11 10:18:13 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:13 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 113
2025-06-11 10:18:14 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 113 条
2025-06-11 10:18:14 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 113 条职位
2025-06-11 10:18:16 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 358 条职位数据
2025-06-11 10:18:19 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:18:19 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=3
2025-06-11 10:18:19 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:18:19 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 135
2025-06-11 10:18:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 135 条
2025-06-11 10:18:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 135 条职位
2025-06-11 10:18:22 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:22 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 111
2025-06-11 10:18:24 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 111 条
2025-06-11 10:18:24 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 111 条职位
2025-06-11 10:18:26 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:26 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 133
2025-06-11 10:18:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 133 条
2025-06-11 10:18:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 133 条职位
2025-06-11 10:18:29 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 379 条职位数据
2025-06-11 10:18:32 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:18:32 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=4
2025-06-11 10:18:32 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:18:32 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 147
2025-06-11 10:18:34 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 147 条
2025-06-11 10:18:34 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 147 条职位
2025-06-11 10:18:35 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:35 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 124
2025-06-11 10:18:36 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 124 条
2025-06-11 10:18:36 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 124 条职位
2025-06-11 10:18:38 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:38 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 112
2025-06-11 10:18:39 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 112 条
2025-06-11 10:18:39 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 112 条职位
2025-06-11 10:18:41 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 383 条职位数据
2025-06-11 10:18:44 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:18:44 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=5
2025-06-11 10:18:44 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:18:44 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 133
2025-06-11 10:18:45 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 133 条
2025-06-11 10:18:45 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 133 条职位
2025-06-11 10:18:46 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:46 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 144
2025-06-11 10:18:48 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 144 条
2025-06-11 10:18:48 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 144 条职位
2025-06-11 10:18:49 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:49 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 126
2025-06-11 10:18:51 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 126 条
2025-06-11 10:18:51 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 126 条职位
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 403 条职位数据
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1732 条有效职位数据
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1732 条职位
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1732 条职位，耗时 54.92 秒
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_101853.json
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 软件工程师
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 软件工程师
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 112
2025-06-11 10:20:28 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 112 条
2025-06-11 10:20:28 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 112 条职位
2025-06-11 10:20:29 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:20:29 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 114
2025-06-11 10:20:30 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 114 条
2025-06-11 10:20:30 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 114 条职位
2025-06-11 10:20:32 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:20:32 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 134
2025-06-11 10:20:33 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 134 条
2025-06-11 10:20:33 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 134 条职位
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 360 条职位数据
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 355 条有效职位数据
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 355 条职位
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 355 条职位，耗时 8.73 秒
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_102035.json
2025-06-11 10:20:36 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_102035.xlsx
2025-06-11 10:20:36 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 355 条职位数据
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: Python工程师
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: Python工程师
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 143
2025-06-11 10:28:19 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 143 条
2025-06-11 10:28:19 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 143 条职位
2025-06-11 10:28:20 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:28:20 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 128
2025-06-11 10:28:22 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 128 条
2025-06-11 10:28:22 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 128 条职位
2025-06-11 10:28:24 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:28:24 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 124
2025-06-11 10:28:25 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 124 条
2025-06-11 10:28:25 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 124 条职位
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 395 条职位数据
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 384 条有效职位数据
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 384 条职位
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 384 条职位，耗时 9.43 秒
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_102827.json
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_102827.xlsx
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 384 条职位数据
2025-06-11 10:36:35 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:36:35 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: Java开发工程师
2025-06-11 10:36:35 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:36:35 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:36:35 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:36:35 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Java%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:36:35 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:36:35 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 133
2025-06-11 10:36:37 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 133 条
2025-06-11 10:36:37 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 133 条职位
2025-06-11 10:36:38 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:36:38 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 133
2025-06-11 10:36:40 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 133 条
2025-06-11 10:36:40 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 133 条职位
2025-06-11 10:36:41 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:36:41 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 109
2025-06-11 10:36:42 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 109 条
2025-06-11 10:36:42 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 109 条职位
2025-06-11 10:36:44 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 375 条职位数据
2025-06-11 10:36:46 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:36:46 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Java%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=2
2025-06-11 10:36:46 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:36:46 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 137
2025-06-11 10:36:48 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 137 条
2025-06-11 10:36:48 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 137 条职位
2025-06-11 10:36:50 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:36:50 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 107
2025-06-11 10:36:51 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 107 条
2025-06-11 10:36:51 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 107 条职位
2025-06-11 10:36:53 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:36:53 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 149
2025-06-11 10:36:54 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 149 条
2025-06-11 10:36:54 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 149 条职位
2025-06-11 10:36:55 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 393 条职位数据
2025-06-11 10:36:59 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:36:59 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Java%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=3
2025-06-11 10:36:59 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:36:59 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 139
2025-06-11 10:37:00 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 139 条
2025-06-11 10:37:00 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 139 条职位
2025-06-11 10:37:01 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:37:01 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 101
2025-06-11 10:37:02 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 101 条
2025-06-11 10:37:02 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 101 条职位
2025-06-11 10:37:04 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:37:04 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 123
2025-06-11 10:37:06 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 123 条
2025-06-11 10:37:06 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 123 条职位
2025-06-11 10:37:07 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 363 条职位数据
2025-06-11 10:37:10 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:37:10 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Java%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=4
2025-06-11 10:37:10 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:37:10 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 120
2025-06-11 10:37:11 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 120 条
2025-06-11 10:37:11 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 120 条职位
2025-06-11 10:37:13 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:37:13 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 101
2025-06-11 10:37:14 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 101 条
2025-06-11 10:37:14 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 101 条职位
2025-06-11 10:37:15 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:37:15 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 106
2025-06-11 10:37:17 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 106 条
2025-06-11 10:37:17 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 106 条职位
2025-06-11 10:37:18 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 327 条职位数据
2025-06-11 10:37:21 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:37:21 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Java%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=5
2025-06-11 10:37:21 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:37:21 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 149
2025-06-11 10:37:23 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 149 条
2025-06-11 10:37:23 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 149 条职位
2025-06-11 10:37:25 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:37:25 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 144
2025-06-11 10:37:26 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 144 条
2025-06-11 10:37:26 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 144 条职位
2025-06-11 10:37:28 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:37:28 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 115
2025-06-11 10:37:29 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 115 条
2025-06-11 10:37:29 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 115 条职位
2025-06-11 10:37:31 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 408 条职位数据
2025-06-11 10:37:31 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1690 条有效职位数据
2025-06-11 10:37:31 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1690 条职位
2025-06-11 10:37:31 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1690 条职位，耗时 55.73 秒
2025-06-11 10:37:31 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_103731.json
2025-06-11 10:37:51 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 10:37:51 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: Java开发工程师
2025-06-11 10:37:51 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 10:37:51 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 10:37:51 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 10:37:51 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 10:37:57 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:37:57 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: Java开发工程师
2025-06-11 10:37:57 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:37:57 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 10:37:57 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:37:57 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Java%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:37:57 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:37:57 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 139
2025-06-11 10:37:58 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 139 条
2025-06-11 10:37:58 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 139 条职位
2025-06-11 10:38:00 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:38:00 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 120
2025-06-11 10:38:01 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 120 条
2025-06-11 10:38:01 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 120 条职位
2025-06-11 10:38:02 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:38:02 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 134
2025-06-11 10:38:04 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 134 条
2025-06-11 10:38:04 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 134 条职位
2025-06-11 10:38:05 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 393 条职位数据
2025-06-11 10:38:05 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 380 条有效职位数据
2025-06-11 10:38:05 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 380 条职位
2025-06-11 10:38:05 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 380 条职位，耗时 8.45 秒
2025-06-11 10:38:05 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_103805.json
2025-06-11 10:38:06 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_103805.xlsx
2025-06-11 10:38:06 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 380 条职位数据
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: Python开发工程师
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 143
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:45:33 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:45:33 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:45:33 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:45:33 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:45:35 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 143 条
2025-06-11 10:45:35 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 143 条职位
2025-06-11 10:45:37 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:45:37 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 128
2025-06-11 10:45:37 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:45:37 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:45:37 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:45:37 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:45:37 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:45:38 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 128 条
2025-06-11 10:45:38 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 128 条职位
2025-06-11 10:45:40 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:45:40 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 128
2025-06-11 10:45:40 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:45:40 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:45:40 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:45:40 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:45:41 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 128 条
2025-06-11 10:45:41 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 128 条职位
2025-06-11 10:45:43 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 399 条职位数据
2025-06-11 10:45:46 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:45:46 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=2
2025-06-11 10:45:46 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:45:46 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 120
2025-06-11 10:45:46 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:45:46 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:45:46 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:45:46 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:45:46 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:45:48 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 120 条
2025-06-11 10:45:48 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 120 条职位
2025-06-11 10:45:49 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:45:49 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 115
2025-06-11 10:45:49 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:45:49 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:45:49 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:45:49 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:45:49 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:45:50 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 115 条
2025-06-11 10:45:50 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 115 条职位
2025-06-11 10:45:51 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:45:51 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 107
2025-06-11 10:45:51 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:45:51 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:45:51 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:45:51 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:45:52 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 107 条
2025-06-11 10:45:52 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 107 条职位
2025-06-11 10:45:54 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 342 条职位数据
2025-06-11 10:45:58 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:45:58 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=3
2025-06-11 10:45:58 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:45:58 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 102
2025-06-11 10:45:58 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:45:58 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:45:58 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:45:58 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:45:58 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:45:59 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 102 条
2025-06-11 10:45:59 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 102 条职位
2025-06-11 10:46:01 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:46:01 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 146
2025-06-11 10:46:01 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:46:01 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:46:01 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:01 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:46:01 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:46:02 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 146 条
2025-06-11 10:46:02 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 146 条职位
2025-06-11 10:46:04 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:46:04 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 150
2025-06-11 10:46:04 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:46:04 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:46:04 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:04 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:46:05 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 150 条
2025-06-11 10:46:05 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 150 条职位
2025-06-11 10:46:07 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 398 条职位数据
2025-06-11 10:46:11 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:46:11 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=4
2025-06-11 10:46:11 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:46:11 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 110
2025-06-11 10:46:11 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:46:11 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:46:11 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:46:11 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:46:11 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:46:12 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 110 条
2025-06-11 10:46:12 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 110 条职位
2025-06-11 10:46:14 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:46:14 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 137
2025-06-11 10:46:14 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:46:14 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:46:14 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:14 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:46:14 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:46:15 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 137 条
2025-06-11 10:46:15 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 137 条职位
2025-06-11 10:46:17 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:46:17 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 142
2025-06-11 10:46:17 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:46:17 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:46:17 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:17 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:46:18 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 142 条
2025-06-11 10:46:18 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 142 条职位
2025-06-11 10:46:20 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 389 条职位数据
2025-06-11 10:46:23 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:46:23 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=5
2025-06-11 10:46:23 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:46:23 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 113
2025-06-11 10:46:23 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:46:23 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:46:23 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:46:23 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:46:23 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:46:24 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 113 条
2025-06-11 10:46:24 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 113 条职位
2025-06-11 10:46:25 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:46:25 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 126
2025-06-11 10:46:25 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:46:25 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:46:25 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:25 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:46:25 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:46:27 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 126 条
2025-06-11 10:46:27 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 126 条职位
2025-06-11 10:46:29 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:46:29 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 103
2025-06-11 10:46:29 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:46:29 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:46:29 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:29 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:46:30 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 103 条
2025-06-11 10:46:30 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 103 条职位
2025-06-11 10:46:31 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 342 条职位数据
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1710 条有效职位数据
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1710 条职位
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1710 条职位，耗时 58.19 秒
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 机器学习算法
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%AE%97%E6%B3%95&page=1
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 143
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:46:32 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 15.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:46:32 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:46:32 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:46:32 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:46:33 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 143 条
2025-06-11 10:46:33 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 143 条职位
2025-06-11 10:46:35 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:46:35 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 113
2025-06-11 10:46:35 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:46:35 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:46:35 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:35 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:46:35 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:46:36 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 113 条
2025-06-11 10:46:36 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 113 条职位
2025-06-11 10:46:37 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:46:37 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 135
2025-06-11 10:46:37 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:46:37 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:46:37 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:37 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:46:39 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 135 条
2025-06-11 10:46:39 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 135 条职位
2025-06-11 10:46:40 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 391 条职位数据
2025-06-11 10:46:43 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:46:43 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%AE%97%E6%B3%95&page=2
2025-06-11 10:46:43 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:46:43 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 138
2025-06-11 10:46:43 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:46:43 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 15.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:46:43 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:46:43 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:46:43 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:46:44 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 138 条
2025-06-11 10:46:44 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 138 条职位
2025-06-11 10:46:46 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:46:46 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 134
2025-06-11 10:46:46 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:46:46 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:46:46 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:46 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:46:46 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:46:47 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 134 条
2025-06-11 10:46:47 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 134 条职位
2025-06-11 10:46:49 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:46:49 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 146
2025-06-11 10:46:49 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:46:49 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:46:49 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:49 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:46:51 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 146 条
2025-06-11 10:46:51 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 146 条职位
2025-06-11 10:46:52 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 418 条职位数据
2025-06-11 10:46:55 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:46:55 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%AE%97%E6%B3%95&page=3
2025-06-11 10:46:55 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:46:55 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 131
2025-06-11 10:46:55 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:46:55 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 15.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:46:55 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:46:55 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:46:55 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:46:57 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 131 条
2025-06-11 10:46:57 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 131 条职位
2025-06-11 10:46:58 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:46:58 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 122
2025-06-11 10:46:58 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:46:58 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:46:58 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:46:58 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:46:58 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:47:00 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 122 条
2025-06-11 10:47:00 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 122 条职位
2025-06-11 10:47:01 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:47:01 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 102
2025-06-11 10:47:01 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:47:01 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:47:01 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:01 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:47:02 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 102 条
2025-06-11 10:47:02 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 102 条职位
2025-06-11 10:47:03 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 355 条职位数据
2025-06-11 10:47:07 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:47:07 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%AE%97%E6%B3%95&page=4
2025-06-11 10:47:07 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:47:07 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 120
2025-06-11 10:47:07 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:47:07 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 15.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:47:07 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:47:07 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:47:07 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:47:08 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 120 条
2025-06-11 10:47:08 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 120 条职位
2025-06-11 10:47:10 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:47:10 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 137
2025-06-11 10:47:10 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:47:10 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:47:10 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:10 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:47:10 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:47:11 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 137 条
2025-06-11 10:47:11 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 137 条职位
2025-06-11 10:47:12 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:47:12 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 143
2025-06-11 10:47:12 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:47:12 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:47:12 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:12 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:47:14 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 143 条
2025-06-11 10:47:14 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 143 条职位
2025-06-11 10:47:15 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 400 条职位数据
2025-06-11 10:47:18 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:47:18 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E7%AE%97%E6%B3%95&page=5
2025-06-11 10:47:18 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:47:18 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 133
2025-06-11 10:47:18 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:47:18 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 15.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:47:18 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:47:18 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:47:18 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:47:20 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 133 条
2025-06-11 10:47:20 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 133 条职位
2025-06-11 10:47:21 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:47:21 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 150
2025-06-11 10:47:21 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:47:21 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:47:21 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:21 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:47:21 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:47:23 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 150 条
2025-06-11 10:47:23 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 150 条职位
2025-06-11 10:47:25 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:47:25 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 132
2025-06-11 10:47:25 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '机器学习算法' 相关的职位模板
2025-06-11 10:47:25 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:47:25 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:25 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '机器学习算法'
2025-06-11 10:47:26 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 132 条
2025-06-11 10:47:26 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 132 条职位
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 415 条职位数据
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1794 条有效职位数据
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1794 条职位
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1794 条职位，耗时 56.54 秒
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 前端React开发
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E5%89%8D%E7%AB%AFReact%E5%BC%80%E5%8F%91&page=1
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 111
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:47:28 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:47:28 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:47:28 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 14.5 - 包含用户关键词(2次), 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:47:28 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:47:29 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 111 条
2025-06-11 10:47:29 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 111 条职位
2025-06-11 10:47:31 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:47:31 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 141
2025-06-11 10:47:31 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:47:31 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:47:31 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:31 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:47:31 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:47:33 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 141 条
2025-06-11 10:47:33 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 141 条职位
2025-06-11 10:47:34 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:47:34 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 122
2025-06-11 10:47:34 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:47:34 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:47:34 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:34 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:47:36 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 122 条
2025-06-11 10:47:36 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 122 条职位
2025-06-11 10:47:38 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 374 条职位数据
2025-06-11 10:47:41 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:47:41 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E5%89%8D%E7%AB%AFReact%E5%BC%80%E5%8F%91&page=2
2025-06-11 10:47:41 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:47:41 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 102
2025-06-11 10:47:41 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:47:41 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:47:41 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:47:41 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 14.5 - 包含用户关键词(2次), 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:47:41 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:47:43 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 102 条
2025-06-11 10:47:43 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 102 条职位
2025-06-11 10:47:44 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:47:44 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 123
2025-06-11 10:47:44 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:47:44 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:47:44 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:44 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:47:44 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:47:45 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 123 条
2025-06-11 10:47:45 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 123 条职位
2025-06-11 10:47:47 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:47:47 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 130
2025-06-11 10:47:47 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:47:47 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:47:47 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:47 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:47:48 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 130 条
2025-06-11 10:47:48 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 130 条职位
2025-06-11 10:47:49 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 355 条职位数据
2025-06-11 10:47:53 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:47:53 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E5%89%8D%E7%AB%AFReact%E5%BC%80%E5%8F%91&page=3
2025-06-11 10:47:53 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:47:53 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 109
2025-06-11 10:47:53 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:47:53 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:47:53 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:47:53 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 14.5 - 包含用户关键词(2次), 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:47:53 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:47:54 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 109 条
2025-06-11 10:47:54 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 109 条职位
2025-06-11 10:47:56 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:47:56 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 118
2025-06-11 10:47:56 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:47:56 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:47:56 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:56 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:47:56 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:47:57 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 118 条
2025-06-11 10:47:57 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 118 条职位
2025-06-11 10:47:59 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:47:59 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 135
2025-06-11 10:47:59 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:47:59 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:47:59 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:47:59 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:48:00 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 135 条
2025-06-11 10:48:00 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 135 条职位
2025-06-11 10:48:02 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 362 条职位数据
2025-06-11 10:48:06 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:48:06 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E5%89%8D%E7%AB%AFReact%E5%BC%80%E5%8F%91&page=4
2025-06-11 10:48:06 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:48:06 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 105
2025-06-11 10:48:06 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:48:06 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:48:06 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:48:06 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 14.5 - 包含用户关键词(2次), 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:48:06 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:48:07 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 105 条
2025-06-11 10:48:07 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 105 条职位
2025-06-11 10:48:08 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:48:08 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 133
2025-06-11 10:48:08 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:48:08 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:48:08 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:08 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:48:08 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:48:09 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 133 条
2025-06-11 10:48:09 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 133 条职位
2025-06-11 10:48:10 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:48:10 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 148
2025-06-11 10:48:10 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:48:10 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:48:10 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:10 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:48:12 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 148 条
2025-06-11 10:48:12 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 148 条职位
2025-06-11 10:48:13 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 386 条职位数据
2025-06-11 10:48:16 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:48:16 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E5%89%8D%E7%AB%AFReact%E5%BC%80%E5%8F%91&page=5
2025-06-11 10:48:16 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:48:16 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 100
2025-06-11 10:48:16 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:48:16 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:48:16 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:48:16 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 14.5 - 包含用户关键词(2次), 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:48:16 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:48:17 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 100 条
2025-06-11 10:48:17 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 100 条职位
2025-06-11 10:48:18 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:48:18 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 140
2025-06-11 10:48:18 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:48:18 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:48:18 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:18 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:48:18 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:48:20 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 140 条
2025-06-11 10:48:20 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 140 条职位
2025-06-11 10:48:22 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:48:22 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 135
2025-06-11 10:48:22 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '前端React开发' 相关的职位模板
2025-06-11 10:48:22 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:48:22 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:22 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '前端React开发'
2025-06-11 10:48:23 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 135 条
2025-06-11 10:48:23 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 135 条职位
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 375 条职位数据
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1692 条有效职位数据
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1692 条职位
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1692 条职位，耗时 56.27 秒
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 数据分析师
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90%E5%B8%88&page=1
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 109
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:24 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 15.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:48:24 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:48:24 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:48:24 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:25 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 109 条
2025-06-11 10:48:25 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 109 条职位
2025-06-11 10:48:27 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:48:27 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 104
2025-06-11 10:48:27 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:27 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:48:27 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:27 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:48:27 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:28 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 104 条
2025-06-11 10:48:28 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 104 条职位
2025-06-11 10:48:29 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:48:29 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 126
2025-06-11 10:48:29 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:29 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:48:29 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:29 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:31 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 126 条
2025-06-11 10:48:31 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 126 条职位
2025-06-11 10:48:32 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 339 条职位数据
2025-06-11 10:48:34 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:48:34 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90%E5%B8%88&page=2
2025-06-11 10:48:34 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:48:34 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 103
2025-06-11 10:48:34 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:34 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 15.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:48:34 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:48:34 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:48:34 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:35 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 103 条
2025-06-11 10:48:35 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 103 条职位
2025-06-11 10:48:37 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:48:37 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 112
2025-06-11 10:48:37 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:37 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:48:37 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:37 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:48:37 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:39 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 112 条
2025-06-11 10:48:39 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 112 条职位
2025-06-11 10:48:40 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:48:40 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 135
2025-06-11 10:48:40 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:40 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:48:40 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:40 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:41 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 135 条
2025-06-11 10:48:41 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 135 条职位
2025-06-11 10:48:42 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 350 条职位数据
2025-06-11 10:48:45 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:48:45 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90%E5%B8%88&page=3
2025-06-11 10:48:45 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:48:45 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 127
2025-06-11 10:48:45 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:45 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 15.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:48:45 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:48:45 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:48:45 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:47 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 127 条
2025-06-11 10:48:47 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 127 条职位
2025-06-11 10:48:48 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:48:48 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 101
2025-06-11 10:48:48 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:48 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:48:48 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:48 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:48:48 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:50 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 101 条
2025-06-11 10:48:50 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 101 条职位
2025-06-11 10:48:51 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:48:51 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 112
2025-06-11 10:48:51 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:51 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:48:51 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:48:51 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:52 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 112 条
2025-06-11 10:48:52 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 112 条职位
2025-06-11 10:48:54 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 340 条职位数据
2025-06-11 10:48:57 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:48:57 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90%E5%B8%88&page=4
2025-06-11 10:48:57 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:48:57 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 105
2025-06-11 10:48:57 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:48:57 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 15.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:48:57 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 13.0 - 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:48:57 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:48:57 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:48:59 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 105 条
2025-06-11 10:48:59 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 105 条职位
2025-06-11 10:49:00 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:49:00 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 126
2025-06-11 10:49:00 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 '数据分析师' 相关的职位模板
2025-06-11 10:49:00 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:49:00 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:49:00 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:49:00 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 '数据分析师'
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: Python开发工程师
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 107
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:53:39 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:53:39 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:53:39 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:53:39 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:53:40 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 107 条
2025-06-11 10:53:40 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 107 条职位
2025-06-11 10:53:42 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:53:42 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 140
2025-06-11 10:53:42 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:53:42 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:53:42 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:53:42 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:53:42 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:53:43 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 140 条
2025-06-11 10:53:43 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 140 条职位
2025-06-11 10:53:45 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:53:45 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 145
2025-06-11 10:53:45 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:53:45 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:53:45 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:53:45 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:53:47 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 145 条
2025-06-11 10:53:47 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 145 条职位
2025-06-11 10:53:48 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 392 条职位数据
2025-06-11 10:53:51 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:53:51 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=2
2025-06-11 10:53:51 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:53:51 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 138
2025-06-11 10:53:51 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:53:51 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:53:51 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:53:51 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:53:51 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:53:53 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 138 条
2025-06-11 10:53:53 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 138 条职位
2025-06-11 10:53:54 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:53:54 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 109
2025-06-11 10:53:54 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:53:54 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:53:54 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:53:54 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:53:54 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:53:56 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 109 条
2025-06-11 10:53:56 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 109 条职位
2025-06-11 10:53:57 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:53:57 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 114
2025-06-11 10:53:57 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:53:57 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:53:57 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:53:57 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:53:58 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 114 条
2025-06-11 10:53:58 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 114 条职位
2025-06-11 10:54:00 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 361 条职位数据
2025-06-11 10:54:02 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:54:02 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=3
2025-06-11 10:54:02 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:54:02 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 136
2025-06-11 10:54:02 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:54:02 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:54:02 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:54:02 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:54:02 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:54:04 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 136 条
2025-06-11 10:54:04 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 136 条职位
2025-06-11 10:54:05 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:54:05 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 148
2025-06-11 10:54:05 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:54:05 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:54:05 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:54:05 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:54:05 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:54:06 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 148 条
2025-06-11 10:54:06 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 148 条职位
2025-06-11 10:54:08 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:54:08 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 142
2025-06-11 10:54:08 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:54:08 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:54:08 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:54:08 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:54:09 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 142 条
2025-06-11 10:54:09 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 142 条职位
2025-06-11 10:54:11 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 426 条职位数据
2025-06-11 10:54:13 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:54:13 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=4
2025-06-11 10:54:13 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:54:13 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 144
2025-06-11 10:54:13 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:54:13 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:54:13 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:54:13 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:54:13 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:54:15 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 144 条
2025-06-11 10:54:15 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 144 条职位
2025-06-11 10:54:16 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:54:16 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 102
2025-06-11 10:54:16 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:54:16 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:54:16 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:54:16 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:54:16 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:54:18 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 102 条
2025-06-11 10:54:18 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 102 条职位
2025-06-11 10:54:19 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:54:19 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 113
2025-06-11 10:54:19 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:54:19 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:54:19 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:54:19 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:54:20 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 113 条
2025-06-11 10:54:20 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 113 条职位
2025-06-11 10:54:22 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 359 条职位数据
2025-06-11 10:54:26 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:54:26 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88&page=5
2025-06-11 10:54:26 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:54:26 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 133
2025-06-11 10:54:26 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:54:26 - crawler - DEBUG - logger.py:89 - 模板匹配: 高级Java开发工程师 - 相关性得分: 18.5 - 包含用户关键词(1次), 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(5个)
2025-06-11 10:54:26 - crawler - DEBUG - logger.py:89 - 模板匹配: Python数据工程师 - 相关性得分: 16.0 - 包含用户关键词(1次), 匹配核心技术(3个), 匹配职位类型(3个), 匹配行业领域(1个), 匹配通用关键词(3个)
2025-06-11 10:54:26 - crawler - DEBUG - logger.py:89 - 模板匹配: 前端架构师 - 相关性得分: 8.5 - 匹配核心技术(4个), 匹配通用关键词(1个)
2025-06-11 10:54:26 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:54:27 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 133 条
2025-06-11 10:54:27 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 133 条职位
2025-06-11 10:54:29 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:54:29 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 105
2025-06-11 10:54:29 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:54:29 - crawler - DEBUG - logger.py:89 - 模板匹配: 算法工程师 - 相关性得分: 8.0 - 匹配核心技术(2个), 匹配职位类型(2个), 匹配通用关键词(2个)
2025-06-11 10:54:29 - crawler - DEBUG - logger.py:89 - 模板匹配: DevOps工程师 - 相关性得分: 14.5 - 匹配核心技术(5个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:54:29 - crawler - DEBUG - logger.py:89 - 模板匹配: 产品经理 - 相关性得分: 2.5 - 匹配职位类型(1个), 匹配行业领域(1个)
2025-06-11 10:54:29 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 3/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:54:30 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 105 条
2025-06-11 10:54:30 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 105 条职位
2025-06-11 10:54:31 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:54:31 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 111
2025-06-11 10:54:31 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发工程师' 相关的职位模板
2025-06-11 10:54:31 - crawler - DEBUG - logger.py:89 - 模板匹配: 软件测试工程师 - 相关性得分: 7.5 - 匹配职位类型(4个), 匹配通用关键词(3个)
2025-06-11 10:54:31 - crawler - DEBUG - logger.py:89 - 模板匹配: 运维工程师 - 相关性得分: 6.5 - 匹配核心技术(1个), 匹配职位类型(2个), 匹配通用关键词(3个)
2025-06-11 10:54:31 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 2/3 个模板匹配关键词 'Python开发工程师'
2025-06-11 10:54:32 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 111 条
2025-06-11 10:54:32 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 111 条职位
2025-06-11 10:54:34 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 349 条职位数据
2025-06-11 10:54:34 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1736 条有效职位数据
2025-06-11 10:54:34 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1736 条职位
2025-06-11 10:54:34 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1736 条职位，耗时 55.01 秒
