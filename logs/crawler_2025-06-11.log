2025-06-11 09:55:15 - crawler - [32m<PERSON><PERSON><PERSON>[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 软件工程师
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 09:55:15 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 09:55:20 - crawler - [32mIN<PERSON>O[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 09:55:20 - crawler - [32mIN<PERSON><PERSON>[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 软件工程师
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 09:55:20 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 130
2025-06-11 09:55:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 130 条
2025-06-11 09:55:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 130 条职位
2025-06-11 09:55:23 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 09:55:23 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 103
2025-06-11 09:55:24 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 103 条
2025-06-11 09:55:24 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 103 条职位
2025-06-11 09:55:26 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 09:55:26 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 114
2025-06-11 09:55:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 114 条
2025-06-11 09:55:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 114 条职位
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 347 条职位数据
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 343 条有效职位数据
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 343 条职位
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 343 条职位，耗时 8.39 秒
2025-06-11 09:55:28 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_095528.json
2025-06-11 09:55:29 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_095528.xlsx
2025-06-11 09:55:29 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 343 条职位数据
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 软件工程师
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 09:58:47 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 软件工程师
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 09:58:49 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 102
2025-06-11 09:58:51 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 102 条
2025-06-11 09:58:51 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 102 条职位
2025-06-11 09:58:52 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 09:58:52 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 111
2025-06-11 09:58:53 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 111 条
2025-06-11 09:58:53 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 111 条职位
2025-06-11 09:58:55 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 09:58:55 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 143
2025-06-11 09:58:57 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 143 条
2025-06-11 09:58:57 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 143 条职位
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 356 条职位数据
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 347 条有效职位数据
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 347 条职位
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 347 条职位，耗时 8.52 秒
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_095858.json
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_095858.xlsx
2025-06-11 09:58:58 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 347 条职位数据
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: AI
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 10:00:49 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: AI
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=1
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:00:51 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 115
2025-06-11 10:00:52 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 115 条
2025-06-11 10:00:52 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 115 条职位
2025-06-11 10:00:53 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:00:53 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 135
2025-06-11 10:00:54 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 135 条
2025-06-11 10:00:54 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 135 条职位
2025-06-11 10:00:56 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:00:56 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 112
2025-06-11 10:00:57 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 112 条
2025-06-11 10:00:57 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 112 条职位
2025-06-11 10:00:58 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 362 条职位数据
2025-06-11 10:01:02 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:01:02 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=2
2025-06-11 10:01:02 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:01:02 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 132
2025-06-11 10:01:04 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 132 条
2025-06-11 10:01:04 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 132 条职位
2025-06-11 10:01:05 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:01:05 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 108
2025-06-11 10:01:07 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 108 条
2025-06-11 10:01:07 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 108 条职位
2025-06-11 10:01:08 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:01:08 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 126
2025-06-11 10:01:09 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 126 条
2025-06-11 10:01:09 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 126 条职位
2025-06-11 10:01:10 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 366 条职位数据
2025-06-11 10:01:14 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:01:14 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=3
2025-06-11 10:01:14 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:01:14 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 121
2025-06-11 10:01:15 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 121 条
2025-06-11 10:01:15 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 121 条职位
2025-06-11 10:01:17 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:01:17 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 124
2025-06-11 10:01:19 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 124 条
2025-06-11 10:01:19 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 124 条职位
2025-06-11 10:01:21 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:01:21 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 139
2025-06-11 10:01:22 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 139 条
2025-06-11 10:01:22 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 139 条职位
2025-06-11 10:01:24 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 384 条职位数据
2025-06-11 10:01:27 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:01:27 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=4
2025-06-11 10:01:27 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:01:27 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 144
2025-06-11 10:01:29 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 144 条
2025-06-11 10:01:29 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 144 条职位
2025-06-11 10:01:31 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:01:31 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 121
2025-06-11 10:01:32 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 121 条
2025-06-11 10:01:32 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 121 条职位
2025-06-11 10:01:34 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:01:34 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 109
2025-06-11 10:01:35 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 109 条
2025-06-11 10:01:35 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 109 条职位
2025-06-11 10:01:37 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 374 条职位数据
2025-06-11 10:01:39 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:01:39 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=AI&page=5
2025-06-11 10:01:39 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:01:39 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 112
2025-06-11 10:01:40 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 112 条
2025-06-11 10:01:40 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 112 条职位
2025-06-11 10:01:42 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:01:42 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 143
2025-06-11 10:01:43 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 143 条
2025-06-11 10:01:43 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 143 条职位
2025-06-11 10:01:45 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:01:45 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 101
2025-06-11 10:01:46 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 101 条
2025-06-11 10:01:46 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 101 条职位
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 356 条职位数据
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1662 条有效职位数据
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1662 条职位
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1662 条职位，耗时 57.04 秒
2025-06-11 10:01:48 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_100148.json
2025-06-11 10:01:49 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_100148.xlsx
2025-06-11 10:01:49 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 1662 条职位数据
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 通信工程
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 10:06:04 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 通信工程
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=1
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:07 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 106
2025-06-11 10:06:09 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 106 条
2025-06-11 10:06:09 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 106 条职位
2025-06-11 10:06:10 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:10 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 135
2025-06-11 10:06:12 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 135 条
2025-06-11 10:06:12 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 135 条职位
2025-06-11 10:06:13 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:06:13 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 127
2025-06-11 10:06:15 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 127 条
2025-06-11 10:06:15 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 127 条职位
2025-06-11 10:06:17 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 368 条职位数据
2025-06-11 10:06:20 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:06:20 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=2
2025-06-11 10:06:20 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:20 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 105
2025-06-11 10:06:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 105 条
2025-06-11 10:06:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 105 条职位
2025-06-11 10:06:23 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:23 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 129
2025-06-11 10:06:25 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 129 条
2025-06-11 10:06:25 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 129 条职位
2025-06-11 10:06:26 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:06:26 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 115
2025-06-11 10:06:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 115 条
2025-06-11 10:06:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 115 条职位
2025-06-11 10:06:28 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 349 条职位数据
2025-06-11 10:06:31 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:06:31 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=3
2025-06-11 10:06:31 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:31 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 121
2025-06-11 10:06:32 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 121 条
2025-06-11 10:06:32 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 121 条职位
2025-06-11 10:06:34 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:34 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 150
2025-06-11 10:06:35 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 150 条
2025-06-11 10:06:35 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 150 条职位
2025-06-11 10:06:36 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:06:36 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 112
2025-06-11 10:06:37 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 112 条
2025-06-11 10:06:37 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 112 条职位
2025-06-11 10:06:39 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 383 条职位数据
2025-06-11 10:06:42 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:06:42 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=4
2025-06-11 10:06:42 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:42 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 131
2025-06-11 10:06:44 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 131 条
2025-06-11 10:06:44 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 131 条职位
2025-06-11 10:06:45 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:45 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 148
2025-06-11 10:06:47 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 148 条
2025-06-11 10:06:47 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 148 条职位
2025-06-11 10:06:49 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:06:49 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 115
2025-06-11 10:06:50 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 115 条
2025-06-11 10:06:50 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 115 条职位
2025-06-11 10:06:52 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 394 条职位数据
2025-06-11 10:06:55 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:06:55 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E9%80%9A%E4%BF%A1%E5%B7%A5%E7%A8%8B&page=5
2025-06-11 10:06:55 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:06:55 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 113
2025-06-11 10:06:56 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 113 条
2025-06-11 10:06:56 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 113 条职位
2025-06-11 10:06:58 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:06:58 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 111
2025-06-11 10:06:59 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 111 条
2025-06-11 10:06:59 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 111 条职位
2025-06-11 10:07:01 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:07:01 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 127
2025-06-11 10:07:03 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 127 条
2025-06-11 10:07:03 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 127 条职位
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 351 条职位数据
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1669 条有效职位数据
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1669 条职位
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1669 条职位，耗时 57.22 秒
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_100705.json
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_100705.xlsx
2025-06-11 10:07:05 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 1669 条职位数据
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 软件工程师
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 5
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:17:58 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 123
2025-06-11 10:17:59 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 123 条
2025-06-11 10:17:59 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 123 条职位
2025-06-11 10:18:00 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:00 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 142
2025-06-11 10:18:02 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 142 条
2025-06-11 10:18:02 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 142 条职位
2025-06-11 10:18:03 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:03 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 122
2025-06-11 10:18:04 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 122 条
2025-06-11 10:18:04 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 122 条职位
2025-06-11 10:18:06 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 387 条职位数据
2025-06-11 10:18:08 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 10:18:08 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=2
2025-06-11 10:18:08 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:18:08 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 141
2025-06-11 10:18:10 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 141 条
2025-06-11 10:18:10 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 141 条职位
2025-06-11 10:18:11 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:11 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 104
2025-06-11 10:18:12 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 104 条
2025-06-11 10:18:12 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 104 条职位
2025-06-11 10:18:13 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:13 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 113
2025-06-11 10:18:14 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 113 条
2025-06-11 10:18:14 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 113 条职位
2025-06-11 10:18:16 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 358 条职位数据
2025-06-11 10:18:19 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 10:18:19 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=3
2025-06-11 10:18:19 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:18:19 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 135
2025-06-11 10:18:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 135 条
2025-06-11 10:18:21 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 135 条职位
2025-06-11 10:18:22 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:22 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 111
2025-06-11 10:18:24 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 111 条
2025-06-11 10:18:24 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 111 条职位
2025-06-11 10:18:26 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:26 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 133
2025-06-11 10:18:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 133 条
2025-06-11 10:18:27 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 133 条职位
2025-06-11 10:18:29 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 379 条职位数据
2025-06-11 10:18:32 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 10:18:32 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=4
2025-06-11 10:18:32 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:18:32 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 147
2025-06-11 10:18:34 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 147 条
2025-06-11 10:18:34 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 147 条职位
2025-06-11 10:18:35 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:35 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 124
2025-06-11 10:18:36 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 124 条
2025-06-11 10:18:36 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 124 条职位
2025-06-11 10:18:38 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:38 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 112
2025-06-11 10:18:39 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 112 条
2025-06-11 10:18:39 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 112 条职位
2025-06-11 10:18:41 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 383 条职位数据
2025-06-11 10:18:44 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 10:18:44 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=5
2025-06-11 10:18:44 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:18:44 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 133
2025-06-11 10:18:45 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 133 条
2025-06-11 10:18:45 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 133 条职位
2025-06-11 10:18:46 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:18:46 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 144
2025-06-11 10:18:48 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 144 条
2025-06-11 10:18:48 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 144 条职位
2025-06-11 10:18:49 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:18:49 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 126
2025-06-11 10:18:51 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 126 条
2025-06-11 10:18:51 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 126 条职位
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 403 条职位数据
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 1732 条有效职位数据
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 1732 条职位
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 1732 条职位，耗时 54.92 秒
2025-06-11 10:18:53 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_101853.json
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 软件工程师
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 10:20:21 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: 软件工程师
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:20:27 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 112
2025-06-11 10:20:28 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 112 条
2025-06-11 10:20:28 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 112 条职位
2025-06-11 10:20:29 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:20:29 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 114
2025-06-11 10:20:30 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 114 条
2025-06-11 10:20:30 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 114 条职位
2025-06-11 10:20:32 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:20:32 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 134
2025-06-11 10:20:33 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 134 条
2025-06-11 10:20:33 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 134 条职位
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 360 条职位数据
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 355 条有效职位数据
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 355 条职位
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 355 条职位，耗时 8.73 秒
2025-06-11 10:20:35 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_102035.json
2025-06-11 10:20:36 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_102035.xlsx
2025-06-11 10:20:36 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 355 条职位数据
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: Python工程师
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 10:28:12 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: Python工程师
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%B7%A5%E7%A8%8B%E5%B8%88&page=1
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 10:28:17 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 143
2025-06-11 10:28:19 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 143 条
2025-06-11 10:28:19 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 143 条职位
2025-06-11 10:28:20 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 10:28:20 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 128
2025-06-11 10:28:22 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 128 条
2025-06-11 10:28:22 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 128 条职位
2025-06-11 10:28:24 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 10:28:24 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 124
2025-06-11 10:28:25 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 124 条
2025-06-11 10:28:25 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 124 条职位
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 395 条职位数据
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 384 条有效职位数据
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 384 条职位
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 384 条职位，耗时 9.43 秒
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_102827.json
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_102827.xlsx
2025-06-11 10:28:27 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 384 条职位数据
