"""
基础爬虫类
Base crawler class
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from pathlib import Path

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, JsonCssExtractionStrategy

from ..utils import crawler_logger, config, anti_detection
from ..data_processing import DataCleaner, JobPosition


class BaseCrawler(ABC):
    """基础爬虫类"""

    def __init__(self, website_name: str, keyword: str = "计算机科学与技术"):
        self.website_name = website_name
        self.keyword = keyword
        self.website_config = config.get_website_config(website_name)
        self.data_cleaner = DataCleaner()

        # 加载提取模式
        self.extraction_schema = self._load_extraction_schema()

        # 爬虫配置
        self.browser_config = self._get_browser_config()
        self.crawler_config = self._get_crawler_config()
    
    def _load_extraction_schema(self) -> Dict[str, Any]:
        """加载数据提取模式"""
        schema_path = Path(self.website_config.extraction_schema_path)
        
        if not schema_path.exists():
            crawler_logger.warning(f"提取模式文件不存在: {schema_path}")
            return self._get_default_schema()
        
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            crawler_logger.info(f"加载提取模式: {schema_path}")
            return schema
        except Exception as e:
            crawler_logger.error(f"加载提取模式失败: {e}")
            return self._get_default_schema()
    
    @abstractmethod
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认提取模式"""
        pass
    
    def _get_browser_config(self) -> BrowserConfig:
        """获取浏览器配置"""
        # 检查是否使用代理
        use_proxy, proxy_url = anti_detection.should_use_proxy(
            config.anti_detection.proxy_list,
            config.anti_detection.use_proxy
        )
        
        return anti_detection.get_browser_config(
            headless=config.crawler.headless,
            use_proxy=use_proxy,
            proxy_url=proxy_url
        )
    
    def _get_crawler_config(self) -> CrawlerRunConfig:
        """获取爬虫运行配置 - 增强版"""
        return CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(self.extraction_schema),
            js_code=self._get_enhanced_js_code(),
            wait_for_images=False,
            process_iframes=False,
            remove_overlay_elements=True,
            verbose=True,
            delay_before_return_html=5.0,  # 等待5秒让内容加载
            page_timeout=60000  # 60秒超时
        )

    def _get_enhanced_js_code(self) -> str:
        """获取增强的JavaScript代码"""
        base_js = anti_detection.get_js_stealth_code()

        # 添加额外的反检测和内容等待代码
        enhanced_js = f"""
        {base_js}

        // 等待内容加载的增强代码
        (function() {{
            // 等待DOM完全加载
            if (document.readyState !== 'complete') {{
                return new Promise(resolve => {{
                    window.addEventListener('load', resolve);
                }});
            }}

            // 等待可能的异步内容
            setTimeout(() => {{
                // 触发滚动以加载懒加载内容
                window.scrollTo(0, document.body.scrollHeight / 2);
                setTimeout(() => {{
                    window.scrollTo(0, 0);
                }}, 1000);
            }}, 1000);

            // 移除可能的遮罩层
            const overlays = document.querySelectorAll('[class*="overlay"], [class*="mask"], [class*="modal"]');
            overlays.forEach(el => {{
                if (el.style.zIndex > 1000) {{
                    el.style.display = 'none';
                }}
            }});
        }})();
        """

        return enhanced_js
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """爬取搜索页面 - 增强版"""
        search_url = self.website_config.get_search_url(self.keyword)
        url = self._build_search_url(search_url, page)

        crawler_logger.log_crawl_start(self.website_name, url)

        try:
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                # 添加随机延迟
                await anti_detection.random_delay(
                    *config.anti_detection.random_delay_range
                )

                # 执行爬取
                result = await crawler.arun(url=url, config=self.crawler_config)

                if not result.success:
                    crawler_logger.log_crawl_error(
                        self.website_name, url, result.error_message
                    )
                    return []

                # 调试信息
                crawler_logger.debug(f"页面HTML长度: {len(result.html)}")

                # 检查页面内容
                page_analysis = self._analyze_page_content(result.html, url)
                if not page_analysis['has_jobs']:
                    crawler_logger.warning(f"页面可能没有职位数据: {page_analysis['reason']}")

                    # 尝试备用提取方法
                    extracted_data = self._fallback_extraction(result.html)
                    if extracted_data:
                        crawler_logger.info(f"备用提取成功，获取 {len(extracted_data)} 条数据")
                    else:
                        # 保存页面用于调试
                        self._save_debug_page(result.html, url, page)
                        return []
                else:
                    # 解析提取的数据
                    extracted_data = self._parse_extracted_data(result.extracted_content)

                crawler_logger.log_crawl_success(
                    self.website_name, url, len(extracted_data)
                )

                return extracted_data

        except Exception as e:
            crawler_logger.log_crawl_error(self.website_name, url, str(e))
            return []
    
    def _parse_extracted_data(self, extracted_content: str) -> List[Dict[str, Any]]:
        """解析提取的数据"""
        try:
            if not extracted_content:
                return []
            
            data = json.loads(extracted_content)
            
            # 如果是列表，直接返回
            if isinstance(data, list):
                return data
            
            # 如果是字典，尝试获取数据列表
            if isinstance(data, dict):
                # 常见的数据字段名
                for key in ['data', 'items', 'results', 'jobs', 'list']:
                    if key in data and isinstance(data[key], list):
                        return data[key]
                
                # 如果没有找到列表字段，将字典包装为列表
                return [data]
            
            return []
            
        except json.JSONDecodeError as e:
            crawler_logger.error(f"解析JSON数据失败: {e}")
            return []
        except Exception as e:
            crawler_logger.error(f"处理提取数据失败: {e}")
            return []
    
    @abstractmethod
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建搜索URL"""
        pass
    
    @abstractmethod
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取职位详情页"""
        pass
    
    async def crawl_multiple_pages(self, max_pages: int = 5) -> List[JobPosition]:
        """爬取多页数据"""
        all_jobs = []
        
        for page in range(1, max_pages + 1):
            crawler_logger.info(f"爬取第 {page} 页数据")
            
            try:
                # 爬取搜索页面
                jobs_data = await self.crawl_search_page(page)
                
                if not jobs_data:
                    crawler_logger.warning(f"第 {page} 页没有获取到数据")
                    continue
                
                # 清洗数据（传递用户关键词）
                search_url = self.website_config.get_search_url(self.keyword)
                cleaned_jobs = self.data_cleaner.clean_job_list(
                    jobs_data,
                    self.website_name,
                    search_url,
                    self.keyword  # 传递用户关键词
                )
                
                all_jobs.extend(cleaned_jobs)
                
                # 添加页面间延迟
                if page < max_pages:
                    await anti_detection.random_delay(
                        config.crawler.request_delay,
                        config.crawler.request_delay + 2
                    )
                
            except Exception as e:
                crawler_logger.error(f"爬取第 {page} 页失败: {e}")
                continue
        
        crawler_logger.info(f"完成爬取，共获取 {len(all_jobs)} 条有效职位数据")
        return all_jobs
    
    async def crawl_with_retry(self, url: str, max_retries: int = None) -> Optional[Dict[str, Any]]:
        """带重试的爬取"""
        max_retries = max_retries or config.crawler.retry_attempts
        
        for attempt in range(max_retries):
            try:
                async with AsyncWebCrawler(config=self.browser_config) as crawler:
                    result = await crawler.arun(url=url, config=self.crawler_config)
                    
                    if result.success:
                        return self._parse_extracted_data(result.extracted_content)
                    else:
                        crawler_logger.warning(
                            f"爬取失败 (尝试 {attempt + 1}/{max_retries}): {result.error_message}"
                        )
                        
            except Exception as e:
                crawler_logger.warning(
                    f"爬取异常 (尝试 {attempt + 1}/{max_retries}): {e}"
                )
            
            # 重试前等待
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # 指数退避
        
        crawler_logger.error(f"爬取最终失败: {url}")
        return None
    
    def _analyze_page_content(self, html: str, url: str) -> Dict[str, Any]:
        """分析页面内容"""
        analysis = {
            'has_jobs': False,
            'reason': '',
            'suggestions': []
        }

        # 检查页面长度
        if len(html) < 1000:
            analysis['reason'] = '页面内容过短，可能加载失败'
            analysis['suggestions'].append('增加等待时间')
            return analysis

        # 检查是否有反爬虫验证
        anti_bot_keywords = ['验证', 'captcha', 'robot', '人机验证', 'verify', 'challenge']
        if any(keyword in html.lower() for keyword in anti_bot_keywords):
            analysis['reason'] = '遇到反爬虫验证'
            analysis['suggestions'].append('更换User-Agent或使用代理')
            return analysis

        # 检查是否需要登录
        login_keywords = ['login', '登录', 'signin', '请登录']
        if any(keyword in html.lower() for keyword in login_keywords):
            analysis['reason'] = '页面需要登录'
            analysis['suggestions'].append('添加登录功能')
            return analysis

        # 检查是否包含关键词
        if self.keyword in html:
            analysis['has_jobs'] = True
            analysis['reason'] = '页面包含搜索关键词'
        else:
            analysis['reason'] = f'页面不包含关键词: {self.keyword}'
            analysis['suggestions'].append('检查搜索URL是否正确')

        return analysis

    def _fallback_extraction(self, html: str) -> List[Dict[str, Any]]:
        """备用数据提取方法"""
        import re
        from bs4 import BeautifulSoup

        try:
            soup = BeautifulSoup(html, 'html.parser')
            jobs = []

            # 尝试通过常见的职位容器类名查找
            job_containers = soup.find_all(['div', 'li', 'tr'], class_=re.compile(r'job|position|work|item'))

            for container in job_containers[:20]:  # 限制数量避免过多无效数据
                job_data = {}

                # 尝试提取标题
                title_elem = container.find(['a', 'h1', 'h2', 'h3', 'span'], string=re.compile(r'.{2,}'))
                if title_elem:
                    job_data['title'] = title_elem.get_text(strip=True)

                # 尝试提取公司名称
                company_elem = container.find(['a', 'span', 'div'], class_=re.compile(r'company|comp'))
                if company_elem:
                    job_data['company'] = company_elem.get_text(strip=True)

                # 尝试提取薪资
                salary_elem = container.find(['span', 'div'], string=re.compile(r'[0-9]+[kK万元]'))
                if salary_elem:
                    job_data['salary'] = salary_elem.get_text(strip=True)

                # 尝试提取地点
                location_elem = container.find(['span', 'div'], class_=re.compile(r'location|area|addr'))
                if location_elem:
                    job_data['location'] = location_elem.get_text(strip=True)

                # 只有包含基本信息的才添加
                if job_data.get('title') and len(job_data.get('title', '')) > 2:
                    jobs.append(job_data)

            return jobs

        except Exception as e:
            crawler_logger.error(f"备用提取失败: {e}")
            return []

    def _save_debug_page(self, html: str, url: str, page: int):
        """保存页面用于调试"""
        try:
            from pathlib import Path
            import datetime

            debug_dir = Path("data/debug")
            debug_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.website_name}_page{page}_{timestamp}.html"
            filepath = debug_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"<!-- URL: {url} -->\n")
                f.write(f"<!-- Timestamp: {timestamp} -->\n")
                f.write(html)

            crawler_logger.info(f"调试页面已保存: {filepath}")

        except Exception as e:
            crawler_logger.error(f"保存调试页面失败: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        search_url = self.website_config.get_search_url(self.keyword)
        return {
            'website': self.website_name,
            'keyword': self.keyword,
            'base_url': self.website_config.base_url,
            'search_url': search_url,
            'enabled': self.website_config.enabled,
            'data_cleaner_stats': self.data_cleaner.get_statistics()
        }
