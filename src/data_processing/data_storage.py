"""
数据存储模块
Data storage module
"""

import json
import csv
import sqlite3
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import pandas as pd
from sqlalchemy import create_engine, Column, String, DateTime, Text, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from .data_cleaner import JobPosition
from ..utils import storage_logger, config

Base = declarative_base()


class JobPositionDB(Base):
    """职位信息数据库模型"""
    __tablename__ = 'job_positions'

    id = Column(Integer, primary_key=True, autoincrement=True)
    job_id = Column(String(16), unique=True, nullable=False, index=True)
    title = Column(String(200), nullable=False)
    company = Column(String(200), nullable=False)
    location = Column(String(100), nullable=False)
    salary = Column(String(100), nullable=False)
    education = Column(String(50))
    experience = Column(String(50))
    description = Column(Text)
    requirements = Column(Text)
    benefits = Column(Text)
    source_website = Column(String(50), nullable=False)
    source_url = Column(Text)
    job_detail_url = Column(Text, default="")
    crawl_time = Column(DateTime, default=datetime.now)
    created_at = Column(DateTime, default=datetime.now)


class DataStorage:
    """数据存储管理器"""
    
    def __init__(self):
        self.output_dir = Path(config.data_storage.output_directory)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 数据库相关
        self.engine = None
        self.Session = None
        
        if config.data_storage.output_format == "database":
            self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        try:
            self.engine = create_engine(config.data_storage.database_url, echo=False)
            Base.metadata.create_all(self.engine)
            self.Session = sessionmaker(bind=self.engine)
            storage_logger.info(f"数据库初始化成功: {config.data_storage.database_url}")
        except Exception as e:
            storage_logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_jobs(self, jobs: List[JobPosition], format_type: str = None) -> str:
        """保存职位数据"""
        if not jobs:
            storage_logger.warning("没有数据需要保存")
            return ""
        
        format_type = format_type or config.data_storage.output_format
        
        if format_type == "json":
            return self._save_as_json(jobs)
        elif format_type == "csv":
            return self._save_as_csv(jobs)
        elif format_type == "database":
            return self._save_to_database(jobs)
        else:
            raise ValueError(f"不支持的输出格式: {format_type}")
    
    def _save_as_json(self, jobs: List[JobPosition]) -> str:
        """保存为JSON格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"jobs_{timestamp}.json"
        filepath = self.output_dir / filename
        
        # 转换为字典列表
        jobs_data = []
        for job in jobs:
            job_dict = job.dict()
            # 转换datetime为字符串
            job_dict['crawl_time'] = job_dict['crawl_time'].isoformat()
            jobs_data.append(job_dict)
        
        # 保存文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(jobs_data, f, ensure_ascii=False, indent=2)
        
        storage_logger.log_data_saved("JSON", str(filepath), len(jobs))
        return str(filepath)
    
    def _save_as_csv(self, jobs: List[JobPosition]) -> str:
        """保存为CSV格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"jobs_{timestamp}.csv"
        filepath = self.output_dir / filename
        
        # 转换为DataFrame
        jobs_data = []
        for job in jobs:
            job_dict = job.dict()
            # 转换datetime为字符串
            job_dict['crawl_time'] = job_dict['crawl_time'].isoformat()
            jobs_data.append(job_dict)
        
        df = pd.DataFrame(jobs_data)
        
        # 保存CSV文件
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        storage_logger.log_data_saved("CSV", str(filepath), len(jobs))
        return str(filepath)
    
    def _save_to_database(self, jobs: List[JobPosition]) -> str:
        """保存到数据库"""
        if not self.Session:
            raise RuntimeError("数据库未初始化")
        
        session = self.Session()
        saved_count = 0
        updated_count = 0
        
        try:
            for job in jobs:
                # 检查是否已存在
                existing = session.query(JobPositionDB).filter_by(job_id=job.job_id).first()
                
                if existing:
                    # 更新现有记录
                    for key, value in job.dict().items():
                        if key != 'id':  # 跳过主键
                            setattr(existing, key, value)
                    updated_count += 1
                else:
                    # 创建新记录
                    db_job = JobPositionDB(**job.dict())
                    session.add(db_job)
                    saved_count += 1
            
            session.commit()
            
            total_count = saved_count + updated_count
            storage_logger.log_data_saved("Database", config.data_storage.database_url, total_count)
            storage_logger.info(f"数据库操作完成: 新增 {saved_count} 条，更新 {updated_count} 条")
            
            return f"数据库保存完成: 新增 {saved_count} 条，更新 {updated_count} 条"
            
        except Exception as e:
            session.rollback()
            storage_logger.error(f"数据库保存失败: {e}")
            raise
        finally:
            session.close()
    
    def load_jobs_from_json(self, filepath: str) -> List[JobPosition]:
        """从JSON文件加载职位数据"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                jobs_data = json.load(f)
            
            jobs = []
            for job_dict in jobs_data:
                # 转换时间字符串为datetime对象
                if isinstance(job_dict.get('crawl_time'), str):
                    job_dict['crawl_time'] = datetime.fromisoformat(job_dict['crawl_time'])
                
                jobs.append(JobPosition(**job_dict))
            
            storage_logger.info(f"从JSON文件加载 {len(jobs)} 条职位数据: {filepath}")
            return jobs
            
        except Exception as e:
            storage_logger.error(f"加载JSON文件失败: {e}")
            raise
    
    def load_jobs_from_database(self, limit: int = None) -> List[JobPosition]:
        """从数据库加载职位数据"""
        if not self.Session:
            raise RuntimeError("数据库未初始化")
        
        session = self.Session()
        try:
            query = session.query(JobPositionDB)
            if limit:
                query = query.limit(limit)
            
            db_jobs = query.all()
            
            jobs = []
            for db_job in db_jobs:
                job_dict = {
                    'job_id': db_job.job_id,
                    'title': db_job.title,
                    'company': db_job.company,
                    'location': db_job.location,
                    'salary': db_job.salary,
                    'education': db_job.education or "",
                    'experience': db_job.experience or "",
                    'description': db_job.description or "",
                    'requirements': db_job.requirements or "",
                    'benefits': db_job.benefits or "",
                    'source_website': db_job.source_website,
                    'source_url': db_job.source_url or "",
                    'job_detail_url': getattr(db_job, 'job_detail_url', '') or "",
                    'crawl_time': db_job.crawl_time
                }
                jobs.append(JobPosition(**job_dict))
            
            storage_logger.info(f"从数据库加载 {len(jobs)} 条职位数据")
            return jobs
            
        except Exception as e:
            storage_logger.error(f"从数据库加载数据失败: {e}")
            raise
        finally:
            session.close()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        stats = {
            'output_directory': str(self.output_dir),
            'output_format': config.data_storage.output_format,
            'files_count': len(list(self.output_dir.glob('*')))
        }
        
        if config.data_storage.output_format == "database" and self.Session:
            session = self.Session()
            try:
                total_jobs = session.query(JobPositionDB).count()
                stats['database_records'] = total_jobs
                
                # 按网站统计
                website_stats = session.query(
                    JobPositionDB.source_website,
                    session.query(JobPositionDB).filter_by(
                        source_website=JobPositionDB.source_website
                    ).count().label('count')
                ).group_by(JobPositionDB.source_website).all()
                
                stats['by_website'] = {ws[0]: ws[1] for ws in website_stats}
                
            except Exception as e:
                storage_logger.error(f"获取数据库统计信息失败: {e}")
            finally:
                session.close()
        
        return stats
    
    def export_to_excel(self, jobs: List[JobPosition], filename: str = None) -> str:
        """导出到Excel文件，优化网址列显示"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"jobs_export_{timestamp}.xlsx"

        filepath = self.output_dir / filename

        # 转换为DataFrame，重新排列列顺序
        jobs_data = []
        for job in jobs:
            job_dict = job.dict()
            job_dict['crawl_time'] = job_dict['crawl_time'].isoformat()

            # 重新排列字段顺序，将实际网址放在显眼位置
            ordered_dict = {
                '职位名称': job_dict.get('title', ''),
                '公司名称': job_dict.get('company', ''),
                '工作地点': job_dict.get('location', ''),
                '薪资范围': job_dict.get('salary', ''),
                '实际网址': job_dict.get('job_detail_url', ''),
                '学历要求': job_dict.get('education', ''),
                '工作经验': job_dict.get('experience', ''),
                '职位描述': job_dict.get('description', ''),
                '职位要求': job_dict.get('requirements', ''),
                '福利待遇': job_dict.get('benefits', ''),
                '来源网站': job_dict.get('source_website', ''),
                '搜索页链接': job_dict.get('source_url', ''),
                '爬取时间': job_dict.get('crawl_time', ''),
                '职位ID': job_dict.get('job_id', '')
            }
            jobs_data.append(ordered_dict)

        df = pd.DataFrame(jobs_data)

        # 保存Excel文件并设置格式
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows
            from openpyxl import Workbook

            wb = Workbook()
            ws = wb.active
            ws.title = "职位数据"

            # 添加数据
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)

            # 设置标题行样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center")

            # 设置实际网址列为超链接样式
            url_col_index = 5  # "实际网址"列的索引
            url_font = Font(color="0000FF", underline="single")

            for row in range(2, len(jobs_data) + 2):
                cell = ws.cell(row=row, column=url_col_index)
                if cell.value and cell.value.startswith('http'):
                    cell.font = url_font
                    cell.hyperlink = cell.value

            # 调整列宽
            column_widths = {
                'A': 25,  # 职位名称
                'B': 20,  # 公司名称
                'C': 15,  # 工作地点
                'D': 12,  # 薪资范围
                'E': 35,  # 实际网址
                'F': 10,  # 学历要求
                'G': 10,  # 工作经验
                'H': 40,  # 职位描述
                'I': 40,  # 职位要求
                'J': 30,  # 福利待遇
                'K': 15,  # 来源网站
                'L': 35,  # 搜索页链接
                'M': 20,  # 爬取时间
                'N': 15   # 职位ID
            }

            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width

            # 保存文件
            wb.save(filepath)

        except ImportError:
            # 如果openpyxl不可用，使用pandas默认方式
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='职位数据', index=False)

        storage_logger.log_data_saved("Excel", str(filepath), len(jobs))
        return str(filepath)
