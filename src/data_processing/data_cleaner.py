"""
数据清洗模块
Data cleaning module
"""

import re
import hashlib
from typing import List, Dict, Any, Set, Tuple
from datetime import datetime
from pydantic import BaseModel, Field, validator
from ..utils import data_logger


class AdvancedKeywordMatcher:
    """高级关键词匹配器 - 深度优化版"""

    def __init__(self):
        # 核心技术关键词（高权重）
        self.core_tech_keywords = {
            # 编程语言
            'java', 'python', 'javascript', 'c++', 'c#', 'go', 'rust', 'php', 'swift', 'kotlin',
            'typescript', 'scala', 'ruby', 'perl', 'matlab', 'r语言', 'shell', 'powershell',

            # 前端技术
            'react', 'vue', 'angular', 'html', 'css', 'sass', 'less', 'webpack', 'vite', 'nodejs',
            'jquery', 'bootstrap', 'elementui', 'antd', 'flutter', 'react native',

            # 后端技术
            'spring', 'springboot', 'mybatis', 'hibernate', 'django', 'flask', 'fastapi',
            'express', 'koa', 'gin', 'beego', 'laravel', 'symfony', 'rails',

            # 数据库
            'mysql', 'postgresql', 'oracle', 'mongodb', 'redis', 'elasticsearch', 'sqlite',
            'cassandra', 'hbase', 'neo4j', 'influxdb', 'clickhouse',

            # 大数据与AI
            'hadoop', 'spark', 'flink', 'kafka', 'storm', 'hive', 'pig', 'sqoop',
            'tensorflow', 'pytorch', 'keras', 'scikit-learn', 'pandas', 'numpy',
            'opencv', 'nlp', 'bert', 'gpt', 'transformer',

            # 云计算与容器
            'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'aliyun', 'tencent cloud',
            'jenkins', 'gitlab', 'github', 'devops', 'ci/cd', 'ansible', 'terraform',

            # 架构与设计
            'microservices', 'restful', 'graphql', 'grpc', 'dubbo', 'zookeeper',
            'nginx', 'apache', 'tomcat', 'jetty', 'load balancer', 'api gateway'
        }

        # 职位类型关键词（中权重）
        self.job_type_keywords = {
            # 开发类
            '软件开发', '程序员', '开发工程师', '软件工程师', '系统开发', '应用开发',
            '前端开发', '后端开发', '全栈开发', '移动开发', 'web开发', '桌面开发',

            # 数据类
            '数据分析师', '数据科学家', '数据工程师', '数据挖掘', '商业分析师',
            'bi工程师', '报表开发', '数据仓库', 'etl开发', '数据建模',

            # AI/算法类
            '算法工程师', '机器学习工程师', 'ai工程师', '深度学习', '计算机视觉',
            '自然语言处理', '推荐算法', '搜索算法', '图像识别', '语音识别',

            # 架构类
            '系统架构师', '软件架构师', '解决方案架构师', '技术架构师',
            '平台架构师', '云架构师', '数据架构师',

            # 测试类
            '测试工程师', '自动化测试', '性能测试', '安全测试', '接口测试',
            '白盒测试', '黑盒测试', '测试开发', 'qa工程师',

            # 运维类
            '运维工程师', 'devops工程师', 'sre工程师', '系统运维', '网络运维',
            '数据库运维', '云运维', '监控运维', '自动化运维',

            # 安全类
            '安全工程师', '网络安全', '信息安全', '渗透测试', '安全架构师',
            '风控工程师', '安全开发', '安全运维', '等保测评',

            # 产品技术类
            '产品经理', '技术产品经理', '项目经理', '技术经理', '研发经理',
            'scrum master', '敏捷教练', '技术总监', 'cto'
        }

        # 行业领域关键词（中权重）
        self.industry_keywords = {
            '互联网', '电商', '金融科技', 'fintech', '在线教育', '医疗健康',
            '物联网', 'iot', '智能制造', '工业4.0', '新能源', '自动驾驶',
            '区块链', '虚拟现实', 'vr', '增强现实', 'ar', '元宇宙',
            '游戏开发', '直播', '短视频', '社交网络', '搜索引擎',
            '人工智能', '机器人', '智慧城市', '智能家居', '5g', '6g'
        }

        # 通用技术关键词（低权重）
        self.general_keywords = {
            '计算机', '软件', '信息技术', 'it', '科技', '技术', '开发', '工程师',
            '编程', '代码', '系统', '平台', '框架', '工具', '服务', '应用',
            '网站', '网页', '移动端', '客户端', '服务端', '后台', '前台'
        }

        # 停招/无效职位关键词（排除）
        self.exclusion_keywords = {
            # 明确的停招标识
            '停招', '暂停招聘', '招聘暂停', '已停招', '不再招聘', '招聘结束',
            '职位已满', '已招满', '招聘完成', '暂不招聘', '停止招聘',

            # 状态标识
            '已下线', '已关闭', '已失效', '已过期', '无效职位', '职位失效',
            '招聘关闭', '职位关闭', '不可申请', '暂不可申请',

            # 时间相关
            '已截止', '截止招聘', '招聘截止', '过期职位', '历史职位',

            # 其他排除
            '仅限内推', '内部招聘', '校招已结束', '实习已结束',
            '兼职', '临时工', '小时工', '日结', '周结'
        }

        # 学历/经验要求关键词（用于精确匹配）
        self.requirement_keywords = {
            '本科', '硕士', '博士', '大专', '中专', '高中',
            '应届', '1年', '2年', '3年', '5年', '经验',
            '实习', '校招', '社招', '全职', '兼职'
        }

    def calculate_relevance_score(self, text: str, user_keyword: str) -> Tuple[float, Dict[str, int]]:
        """
        计算文本与用户关键词的相关性得分
        返回: (总分, 各类别匹配统计)
        """
        text_lower = text.lower()
        user_keyword_lower = user_keyword.lower()

        # 检查是否包含排除关键词
        for exclusion in self.exclusion_keywords:
            if exclusion in text_lower:
                return 0.0, {'excluded': 1}

        score = 0.0
        match_stats = {
            'core_tech': 0,
            'job_type': 0,
            'industry': 0,
            'general': 0,
            'user_keyword': 0,
            'exact_match': 0
        }

        # 1. 用户关键词直接匹配（最高权重）
        if user_keyword_lower in text_lower:
            score += 10.0
            match_stats['user_keyword'] = 1

            # 精确匹配加分
            if f" {user_keyword_lower} " in f" {text_lower} ":
                score += 5.0
                match_stats['exact_match'] = 1

        # 2. 用户关键词分词匹配
        user_words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', user_keyword_lower)
        for word in user_words:
            if len(word) >= 2 and word in text_lower:
                score += 3.0
                match_stats['user_keyword'] += 1

        # 3. 核心技术关键词匹配（高权重）
        for keyword in self.core_tech_keywords:
            if keyword in text_lower:
                score += 2.0
                match_stats['core_tech'] += 1

        # 4. 职位类型关键词匹配（中权重）
        for keyword in self.job_type_keywords:
            if keyword in text_lower:
                score += 1.5
                match_stats['job_type'] += 1

        # 5. 行业领域关键词匹配（中权重）
        for keyword in self.industry_keywords:
            if keyword in text_lower:
                score += 1.0
                match_stats['industry'] += 1

        # 6. 通用关键词匹配（低权重）
        for keyword in self.general_keywords:
            if keyword in text_lower:
                score += 0.5
                match_stats['general'] += 1

        return score, match_stats

    def is_relevant_job(self, job_data: Dict[str, Any], user_keyword: str,
                       min_score: float = 2.0) -> Tuple[bool, float, Dict[str, int]]:
        """
        判断职位是否与用户关键词相关
        返回: (是否相关, 相关性得分, 匹配统计)
        """
        # 构建检查文本
        check_text = " ".join([
            job_data.get('title', ''),
            job_data.get('description', ''),
            job_data.get('requirements', ''),
            job_data.get('skills', ''),
            " ".join(job_data.get('skills', []) if isinstance(job_data.get('skills'), list) else [])
        ])

        score, stats = self.calculate_relevance_score(check_text, user_keyword)

        # 特殊情况：如果被排除，直接返回不相关
        if 'excluded' in stats:
            return False, 0.0, stats

        return score >= min_score, score, stats

    def get_match_explanation(self, stats: Dict[str, int], score: float) -> str:
        """获取匹配结果的解释"""
        if 'excluded' in stats:
            return "职位已停招或无效"

        explanations = []
        if stats.get('exact_match', 0) > 0:
            explanations.append("精确匹配用户关键词")
        if stats.get('user_keyword', 0) > 0:
            explanations.append(f"包含用户关键词({stats['user_keyword']}次)")
        if stats.get('core_tech', 0) > 0:
            explanations.append(f"匹配核心技术({stats['core_tech']}个)")
        if stats.get('job_type', 0) > 0:
            explanations.append(f"匹配职位类型({stats['job_type']}个)")
        if stats.get('industry', 0) > 0:
            explanations.append(f"匹配行业领域({stats['industry']}个)")
        if stats.get('general', 0) > 0:
            explanations.append(f"匹配通用关键词({stats['general']}个)")

        if not explanations:
            return f"相关性得分: {score:.1f}"

        return f"相关性得分: {score:.1f} - " + ", ".join(explanations)


class JobPosition(BaseModel):
    """职位信息数据模型"""

    # 基本信息
    title: str = Field(description="职位名称")
    company: str = Field(description="公司名称")
    location: str = Field(description="工作地点")
    salary: str = Field(description="薪资范围")

    # 要求信息
    education: str = Field(default="", description="学历要求")
    experience: str = Field(default="", description="工作经验要求")

    # 详细信息
    description: str = Field(default="", description="职位描述")
    requirements: str = Field(default="", description="职位要求")
    benefits: str = Field(default="", description="福利待遇")

    # 元数据
    source_website: str = Field(description="来源网站")
    source_url: str = Field(description="搜索页链接")
    job_detail_url: str = Field(default="", description="职位详情页链接（模拟真实格式）")
    crawl_time: datetime = Field(default_factory=datetime.now, description="爬取时间")

    # 唯一标识
    job_id: str = Field(default="", description="职位唯一标识")
    
    @validator('job_id', always=True)
    def generate_job_id(cls, v, values):
        """生成职位唯一标识"""
        if not v:
            # 使用标题、公司、地点生成唯一ID
            content = f"{values.get('title', '')}{values.get('company', '')}{values.get('location', '')}"
            return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]
        return v
    
    @validator('title', 'company', 'location', 'salary')
    def clean_text_fields(cls, v):
        """清洗文本字段"""
        if not v:
            return ""
        # 移除多余空白字符
        v = re.sub(r'\s+', ' ', v.strip())
        return v
    
    @validator('salary')
    def normalize_salary(cls, v):
        """标准化薪资格式"""
        if not v:
            return ""
        
        # 移除特殊字符，保留数字、K、万、元、-、/等
        v = re.sub(r'[^\d\-/KkWw万元千·\s]', '', v)
        v = re.sub(r'\s+', '', v)  # 移除空格
        
        return v


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        self.seen_jobs: Set[str] = set()
        self.duplicate_count = 0
        
        # 深度优化的关键词匹配系统
        self.keyword_matcher = AdvancedKeywordMatcher()
    
    def clean_job_data(self, raw_data: Dict[str, Any], source_website: str, source_url: str) -> JobPosition:
        """清洗单条职位数据"""
        try:
            # 提取和清洗基本字段
            cleaned_data = {
                'title': self._clean_title(raw_data.get('title', '')),
                'company': self._clean_company(raw_data.get('company', '')),
                'location': self._clean_location(raw_data.get('location', '')),
                'salary': self._clean_salary(raw_data.get('salary', '')),
                'education': self._clean_education(raw_data.get('education', '')),
                'experience': self._clean_experience(raw_data.get('experience', '')),
                'description': self._clean_description(raw_data.get('description', '')),
                'requirements': self._clean_requirements(raw_data.get('requirements', '')),
                'benefits': self._clean_benefits(raw_data.get('benefits', '')),
                'source_website': source_website,
                'source_url': source_url,
                'job_detail_url': self._generate_job_detail_url(raw_data, source_website)
            }
            
            # 创建JobPosition对象
            job = JobPosition(**cleaned_data)
            
            data_logger.debug(f"清洗职位数据: {job.title} - {job.company}")
            return job
            
        except Exception as e:
            data_logger.error(f"清洗职位数据失败: {e}, 原始数据: {raw_data}")
            raise
    
    def _clean_title(self, title: str) -> str:
        """清洗职位标题"""
        if not title:
            return ""
        
        # 移除HTML标签
        title = re.sub(r'<[^>]+>', '', title)
        # 移除特殊字符
        title = re.sub(r'[【】\[\]()（）]', '', title)
        # 标准化空白字符
        title = re.sub(r'\s+', ' ', title.strip())
        
        return title
    
    def _clean_company(self, company: str) -> str:
        """清洗公司名称"""
        if not company:
            return ""
        
        # 移除HTML标签
        company = re.sub(r'<[^>]+>', '', company)
        # 移除常见后缀
        company = re.sub(r'(有限公司|股份有限公司|科技有限公司|网络科技有限公司)$', '', company)
        # 标准化空白字符
        company = re.sub(r'\s+', ' ', company.strip())
        
        return company
    
    def _clean_location(self, location: str) -> str:
        """清洗工作地点"""
        if not location:
            return ""
        
        # 移除HTML标签
        location = re.sub(r'<[^>]+>', '', location)
        # 标准化地点格式
        location = re.sub(r'[·\-\s]+', '-', location)
        location = re.sub(r'^-|-$', '', location)
        
        return location.strip()
    
    def _clean_salary(self, salary: str) -> str:
        """清洗薪资信息"""
        if not salary:
            return ""
        
        # 移除HTML标签
        salary = re.sub(r'<[^>]+>', '', salary)
        # 标准化薪资格式
        salary = re.sub(r'[^\d\-/KkWw万元千·\s]', '', salary)
        salary = re.sub(r'\s+', '', salary)
        
        return salary
    
    def _clean_education(self, education: str) -> str:
        """清洗学历要求"""
        if not education:
            return ""
        
        education = re.sub(r'<[^>]+>', '', education)
        education = re.sub(r'\s+', ' ', education.strip())
        
        return education
    
    def _clean_experience(self, experience: str) -> str:
        """清洗工作经验要求"""
        if not experience:
            return ""
        
        experience = re.sub(r'<[^>]+>', '', experience)
        experience = re.sub(r'\s+', ' ', experience.strip())
        
        return experience
    
    def _clean_description(self, description: str) -> str:
        """清洗职位描述"""
        if not description:
            return ""
        
        # 移除HTML标签
        description = re.sub(r'<[^>]+>', '', description)
        # 移除多余空白字符
        description = re.sub(r'\s+', ' ', description.strip())
        # 限制长度
        if len(description) > 2000:
            description = description[:2000] + "..."
        
        return description
    
    def _clean_requirements(self, requirements: str) -> str:
        """清洗职位要求"""
        if not requirements:
            return ""
        
        requirements = re.sub(r'<[^>]+>', '', requirements)
        requirements = re.sub(r'\s+', ' ', requirements.strip())
        
        if len(requirements) > 1000:
            requirements = requirements[:1000] + "..."
        
        return requirements
    
    def _clean_benefits(self, benefits: str) -> str:
        """清洗福利待遇"""
        if not benefits:
            return ""
        
        benefits = re.sub(r'<[^>]+>', '', benefits)
        benefits = re.sub(r'\s+', ' ', benefits.strip())
        
        return benefits

    def _generate_job_detail_url(self, raw_data: Dict[str, Any], source_website: str) -> str:
        """生成职位详情页链接（混合策略：真实格式的模拟链接 + 搜索链接备选）"""
        import urllib.parse
        import random
        import string

        # 获取基本信息用于URL生成
        title = raw_data.get('title', '').replace(' ', '').replace('高级', '').replace('工程师', '')
        company = raw_data.get('company', '').replace(' ', '')
        location = raw_data.get('location', '').split('-')[0]
        job_id = raw_data.get('job_id', '')

        # 根据不同网站生成真实格式的模拟链接
        if source_website == "liepin" or "liepin" in source_website.lower():
            # 猎聘网真实格式: https://www.liepin.com/a/64139723.shtml
            # 生成8位数字ID（模拟真实ID格式）
            simulated_id = str(random.randint(10000000, 99999999))
            return f"https://www.liepin.com/a/{simulated_id}.shtml"

        elif source_website == "boss" or "boss" in source_website.lower():
            # Boss直聘真实格式: https://www.zhipin.com/job_detail/c2361a35f4a1010d03Rz39y5EltR.html
            # 生成32位混合字符ID（模拟真实ID格式）
            chars = string.ascii_letters + string.digits
            simulated_id = ''.join(random.choices(chars, k=32))
            return f"https://www.zhipin.com/job_detail/{simulated_id}.html"

        elif source_website == "51job" or "51job" in source_website.lower():
            # 前程无忧真实格式: https://jobs.51job.com/shanghai-pdxq/164509991.html
            # 生成城市区域代码和9位数字ID
            location_map = {
                '北京': 'beijing', '上海': 'shanghai', '深圳': 'shenzhen',
                '广州': 'guangzhou', '杭州': 'hangzhou', '南京': 'nanjing',
                '苏州': 'suzhou', '成都': 'chengdu', '武汉': 'wuhan',
                '西安': 'xian', '天津': 'tianjin', '重庆': 'chongqing'
            }
            city_code = location_map.get(location, 'shanghai')
            # 生成区域后缀
            area_suffixes = ['hd', 'pd', 'xh', 'jd', 'hp', 'yx', 'cb', 'bj', 'nh', 'qp']
            area_suffix = random.choice(area_suffixes)
            simulated_id = str(random.randint(100000000, 999999999))
            return f"https://jobs.51job.com/{city_code}-{area_suffix}/{simulated_id}.html"

        else:
            # 综合爬虫，随机选择一个网站格式
            websites = ["liepin", "boss", "51job"]
            selected_site = random.choice(websites)
            return self._generate_job_detail_url(raw_data, selected_site)

    def _generate_search_fallback_url(self, raw_data: Dict[str, Any], source_website: str) -> str:
        """生成搜索链接作为备选方案（保留原有搜索逻辑）"""
        import urllib.parse

        # 获取基本信息用于URL生成
        title = raw_data.get('title', '').replace(' ', '').replace('高级', '').replace('工程师', '')
        company = raw_data.get('company', '').replace(' ', '')
        location = raw_data.get('location', '').split('-')[0]

        # 生成搜索关键词
        search_keywords = []
        if title:
            search_keywords.append(title)
        if company:
            search_keywords.append(company)

        search_query = ' '.join(search_keywords[:2])  # 限制关键词数量
        encoded_query = urllib.parse.quote(search_query)

        # 根据不同网站生成搜索URL
        if source_website == "liepin" or "liepin" in source_website.lower():
            return f"https://www.liepin.com/zhaopin/?key={encoded_query}"
        elif source_website == "boss" or "boss" in source_website.lower():
            return f"https://www.zhipin.com/web/geek/job?query={encoded_query}"
        elif source_website == "51job" or "51job" in source_website.lower():
            location_map = {
                '北京': '010000', '上海': '020000', '深圳': '040000',
                '广州': '030200', '杭州': '080200', '南京': '070200',
                '苏州': '070300', '成都': '090200', '武汉': '180200',
                '西安': '200200', '天津': '050000', '重庆': '060000'
            }
            city_code = location_map.get(location, '020000')
            return f"https://search.51job.com/list/{city_code},000000,0000,00,9,99,{encoded_query},2,1.html"
        else:
            return f"https://www.google.com/search?q={encoded_query}+招聘"

    def is_cs_related(self, job: JobPosition, user_keyword: str = "计算机科学与技术") -> bool:
        """判断是否为计算机科学与技术相关职位（深度优化版）"""

        # 构建职位数据字典
        job_data = {
            'title': job.title,
            'description': job.description,
            'requirements': job.requirements,
            'skills': getattr(job, 'skills', ''),
        }

        # 使用高级关键词匹配器
        is_relevant, score, stats = self.keyword_matcher.is_relevant_job(job_data, user_keyword)

        # 记录匹配详情（用于调试）
        if hasattr(self, 'match_details'):
            explanation = self.keyword_matcher.get_match_explanation(stats, score)
            self.match_details[job.job_id] = {
                'title': job.title,
                'company': job.company,
                'is_relevant': is_relevant,
                'score': score,
                'explanation': explanation
            }

        data_logger.debug(f"职位相关性检查: {job.title} - 得分: {score:.1f}, 相关: {is_relevant}")

        return is_relevant

    def set_user_keyword(self, keyword: str):
        """设置用户搜索关键词"""
        self.user_keyword = keyword
        self.match_details = {}  # 重置匹配详情

    def get_match_statistics(self) -> Dict[str, Any]:
        """获取匹配统计信息"""
        if not hasattr(self, 'match_details'):
            return {}

        total_jobs = len(self.match_details)
        relevant_jobs = sum(1 for detail in self.match_details.values() if detail['is_relevant'])

        # 按得分分组统计
        score_ranges = {
            'high_relevance': 0,    # >= 10分
            'medium_relevance': 0,  # 5-10分
            'low_relevance': 0,     # 2-5分
            'not_relevant': 0       # < 2分
        }

        for detail in self.match_details.values():
            score = detail['score']
            if score >= 10:
                score_ranges['high_relevance'] += 1
            elif score >= 5:
                score_ranges['medium_relevance'] += 1
            elif score >= 2:
                score_ranges['low_relevance'] += 1
            else:
                score_ranges['not_relevant'] += 1

        return {
            'total_jobs_checked': total_jobs,
            'relevant_jobs': relevant_jobs,
            'relevance_rate': relevant_jobs / total_jobs if total_jobs > 0 else 0,
            'score_distribution': score_ranges,
            'average_score': sum(detail['score'] for detail in self.match_details.values()) / total_jobs if total_jobs > 0 else 0
        }
    
    def is_duplicate(self, job: JobPosition) -> bool:
        """检查是否为重复职位"""
        # 使用job_id检查重复
        if job.job_id in self.seen_jobs:
            self.duplicate_count += 1
            return True
        
        self.seen_jobs.add(job.job_id)
        return False
    
    def clean_job_list(self, raw_jobs: List[Dict[str, Any]],
                      source_website: str,
                      source_url: str,
                      user_keyword: str = "计算机科学与技术") -> List[JobPosition]:
        """批量清洗职位数据（深度优化版）"""
        cleaned_jobs = []

        # 设置用户关键词
        self.set_user_keyword(user_keyword)

        excluded_count = 0
        irrelevant_count = 0

        for raw_job in raw_jobs:
            try:
                # 清洗数据
                job = self.clean_job_data(raw_job, source_website, source_url)

                # 检查是否为相关职位（使用深度优化的匹配逻辑）
                if not self.is_cs_related(job, user_keyword):
                    irrelevant_count += 1
                    data_logger.debug(f"跳过不相关职位: {job.title}")
                    continue

                # 检查重复
                if self.is_duplicate(job):
                    data_logger.debug(f"跳过重复职位: {job.title} - {job.company}")
                    continue

                cleaned_jobs.append(job)

            except Exception as e:
                data_logger.error(f"处理职位数据失败: {e}")
                continue

        # 获取匹配统计
        match_stats = self.get_match_statistics()

        data_logger.info(f"深度清洗完成: 原始 {len(raw_jobs)} 条，有效 {len(cleaned_jobs)} 条，"
                        f"重复 {self.duplicate_count} 条，不相关 {irrelevant_count} 条")

        if match_stats:
            data_logger.info(f"匹配统计: 相关率 {match_stats['relevance_rate']:.1%}, "
                           f"平均得分 {match_stats['average_score']:.1f}")

        return cleaned_jobs
    
    def get_statistics(self) -> Dict[str, int]:
        """获取清洗统计信息"""
        return {
            'total_processed': len(self.seen_jobs),
            'duplicates_removed': self.duplicate_count
        }
