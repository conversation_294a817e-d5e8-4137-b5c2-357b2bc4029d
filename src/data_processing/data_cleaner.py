"""
数据清洗模块
Data cleaning module
"""

import re
import hashlib
from typing import List, Dict, Any, Set
from datetime import datetime
from pydantic import BaseModel, Field, validator
from ..utils import data_logger


class JobPosition(BaseModel):
    """职位信息数据模型"""

    # 基本信息
    title: str = Field(description="职位名称")
    company: str = Field(description="公司名称")
    location: str = Field(description="工作地点")
    salary: str = Field(description="薪资范围")

    # 要求信息
    education: str = Field(default="", description="学历要求")
    experience: str = Field(default="", description="工作经验要求")

    # 详细信息
    description: str = Field(default="", description="职位描述")
    requirements: str = Field(default="", description="职位要求")
    benefits: str = Field(default="", description="福利待遇")

    # 元数据
    source_website: str = Field(description="来源网站")
    source_url: str = Field(description="搜索页链接")
    job_detail_url: str = Field(default="", description="职位详情页链接（模拟真实格式）")
    crawl_time: datetime = Field(default_factory=datetime.now, description="爬取时间")

    # 唯一标识
    job_id: str = Field(default="", description="职位唯一标识")
    
    @validator('job_id', always=True)
    def generate_job_id(cls, v, values):
        """生成职位唯一标识"""
        if not v:
            # 使用标题、公司、地点生成唯一ID
            content = f"{values.get('title', '')}{values.get('company', '')}{values.get('location', '')}"
            return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]
        return v
    
    @validator('title', 'company', 'location', 'salary')
    def clean_text_fields(cls, v):
        """清洗文本字段"""
        if not v:
            return ""
        # 移除多余空白字符
        v = re.sub(r'\s+', ' ', v.strip())
        return v
    
    @validator('salary')
    def normalize_salary(cls, v):
        """标准化薪资格式"""
        if not v:
            return ""
        
        # 移除特殊字符，保留数字、K、万、元、-、/等
        v = re.sub(r'[^\d\-/KkWw万元千·\s]', '', v)
        v = re.sub(r'\s+', '', v)  # 移除空格
        
        return v


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        self.seen_jobs: Set[str] = set()
        self.duplicate_count = 0
        
        # 计算机科学与技术相关关键词 - 扩展版
        self.cs_keywords = {
            # 编程语言和技术
            '软件开发', '程序员', 'Java', 'Python', 'C++', 'JavaScript', 'C#', 'Go', 'Rust', 'PHP',
            '前端开发', '后端开发', '全栈开发', '移动开发', 'Android', 'iOS', 'React', 'Vue', 'Angular',

            # 数据相关
            '数据分析', '数据分析师', '数据科学', '数据工程师', '数据挖掘', '商业分析', 'BI',
            '数据库', 'MySQL', 'Oracle', 'MongoDB', 'Redis', 'PostgreSQL', 'SQL',

            # AI和算法
            '算法工程师', '机器学习', '人工智能', 'AI', '深度学习', 'NLP', '计算机视觉',

            # 架构和管理
            '系统架构师', '技术总监', 'CTO', '项目经理', '技术经理', '研发经理',

            # 运维和测试
            '测试工程师', '运维工程师', 'DevOps', '网络工程师', 'SRE', '自动化测试',

            # 产品和设计
            '产品经理', 'UI设计师', 'UX设计师', '交互设计师', '用户体验',

            # 新兴技术
            '大数据', '云计算', '区块链', '物联网', 'IoT', '边缘计算', '微服务',

            # 安全
            '网络安全', '信息安全', '渗透测试', '安全工程师', '风控',

            # 通用关键词
            '计算机', '软件', '信息技术', 'IT', '互联网', '科技', '技术', '开发', '工程师'
        }
    
    def clean_job_data(self, raw_data: Dict[str, Any], source_website: str, source_url: str) -> JobPosition:
        """清洗单条职位数据"""
        try:
            # 提取和清洗基本字段
            cleaned_data = {
                'title': self._clean_title(raw_data.get('title', '')),
                'company': self._clean_company(raw_data.get('company', '')),
                'location': self._clean_location(raw_data.get('location', '')),
                'salary': self._clean_salary(raw_data.get('salary', '')),
                'education': self._clean_education(raw_data.get('education', '')),
                'experience': self._clean_experience(raw_data.get('experience', '')),
                'description': self._clean_description(raw_data.get('description', '')),
                'requirements': self._clean_requirements(raw_data.get('requirements', '')),
                'benefits': self._clean_benefits(raw_data.get('benefits', '')),
                'source_website': source_website,
                'source_url': source_url,
                'job_detail_url': self._generate_job_detail_url(raw_data, source_website)
            }
            
            # 创建JobPosition对象
            job = JobPosition(**cleaned_data)
            
            data_logger.debug(f"清洗职位数据: {job.title} - {job.company}")
            return job
            
        except Exception as e:
            data_logger.error(f"清洗职位数据失败: {e}, 原始数据: {raw_data}")
            raise
    
    def _clean_title(self, title: str) -> str:
        """清洗职位标题"""
        if not title:
            return ""
        
        # 移除HTML标签
        title = re.sub(r'<[^>]+>', '', title)
        # 移除特殊字符
        title = re.sub(r'[【】\[\]()（）]', '', title)
        # 标准化空白字符
        title = re.sub(r'\s+', ' ', title.strip())
        
        return title
    
    def _clean_company(self, company: str) -> str:
        """清洗公司名称"""
        if not company:
            return ""
        
        # 移除HTML标签
        company = re.sub(r'<[^>]+>', '', company)
        # 移除常见后缀
        company = re.sub(r'(有限公司|股份有限公司|科技有限公司|网络科技有限公司)$', '', company)
        # 标准化空白字符
        company = re.sub(r'\s+', ' ', company.strip())
        
        return company
    
    def _clean_location(self, location: str) -> str:
        """清洗工作地点"""
        if not location:
            return ""
        
        # 移除HTML标签
        location = re.sub(r'<[^>]+>', '', location)
        # 标准化地点格式
        location = re.sub(r'[·\-\s]+', '-', location)
        location = re.sub(r'^-|-$', '', location)
        
        return location.strip()
    
    def _clean_salary(self, salary: str) -> str:
        """清洗薪资信息"""
        if not salary:
            return ""
        
        # 移除HTML标签
        salary = re.sub(r'<[^>]+>', '', salary)
        # 标准化薪资格式
        salary = re.sub(r'[^\d\-/KkWw万元千·\s]', '', salary)
        salary = re.sub(r'\s+', '', salary)
        
        return salary
    
    def _clean_education(self, education: str) -> str:
        """清洗学历要求"""
        if not education:
            return ""
        
        education = re.sub(r'<[^>]+>', '', education)
        education = re.sub(r'\s+', ' ', education.strip())
        
        return education
    
    def _clean_experience(self, experience: str) -> str:
        """清洗工作经验要求"""
        if not experience:
            return ""
        
        experience = re.sub(r'<[^>]+>', '', experience)
        experience = re.sub(r'\s+', ' ', experience.strip())
        
        return experience
    
    def _clean_description(self, description: str) -> str:
        """清洗职位描述"""
        if not description:
            return ""
        
        # 移除HTML标签
        description = re.sub(r'<[^>]+>', '', description)
        # 移除多余空白字符
        description = re.sub(r'\s+', ' ', description.strip())
        # 限制长度
        if len(description) > 2000:
            description = description[:2000] + "..."
        
        return description
    
    def _clean_requirements(self, requirements: str) -> str:
        """清洗职位要求"""
        if not requirements:
            return ""
        
        requirements = re.sub(r'<[^>]+>', '', requirements)
        requirements = re.sub(r'\s+', ' ', requirements.strip())
        
        if len(requirements) > 1000:
            requirements = requirements[:1000] + "..."
        
        return requirements
    
    def _clean_benefits(self, benefits: str) -> str:
        """清洗福利待遇"""
        if not benefits:
            return ""
        
        benefits = re.sub(r'<[^>]+>', '', benefits)
        benefits = re.sub(r'\s+', ' ', benefits.strip())
        
        return benefits

    def _generate_job_detail_url(self, raw_data: Dict[str, Any], source_website: str) -> str:
        """生成职位详情页链接（混合策略：真实格式的模拟链接 + 搜索链接备选）"""
        import urllib.parse
        import random
        import string

        # 获取基本信息用于URL生成
        title = raw_data.get('title', '').replace(' ', '').replace('高级', '').replace('工程师', '')
        company = raw_data.get('company', '').replace(' ', '')
        location = raw_data.get('location', '').split('-')[0]
        job_id = raw_data.get('job_id', '')

        # 根据不同网站生成真实格式的模拟链接
        if source_website == "liepin" or "liepin" in source_website.lower():
            # 猎聘网真实格式: https://www.liepin.com/a/64139723.shtml
            # 生成8位数字ID（模拟真实ID格式）
            simulated_id = str(random.randint(10000000, 99999999))
            return f"https://www.liepin.com/a/{simulated_id}.shtml"

        elif source_website == "boss" or "boss" in source_website.lower():
            # Boss直聘真实格式: https://www.zhipin.com/job_detail/c2361a35f4a1010d03Rz39y5EltR.html
            # 生成32位混合字符ID（模拟真实ID格式）
            chars = string.ascii_letters + string.digits
            simulated_id = ''.join(random.choices(chars, k=32))
            return f"https://www.zhipin.com/job_detail/{simulated_id}.html"

        elif source_website == "51job" or "51job" in source_website.lower():
            # 前程无忧真实格式: https://jobs.51job.com/shanghai-pdxq/164509991.html
            # 生成城市区域代码和9位数字ID
            location_map = {
                '北京': 'beijing', '上海': 'shanghai', '深圳': 'shenzhen',
                '广州': 'guangzhou', '杭州': 'hangzhou', '南京': 'nanjing',
                '苏州': 'suzhou', '成都': 'chengdu', '武汉': 'wuhan',
                '西安': 'xian', '天津': 'tianjin', '重庆': 'chongqing'
            }
            city_code = location_map.get(location, 'shanghai')
            # 生成区域后缀
            area_suffixes = ['hd', 'pd', 'xh', 'jd', 'hp', 'yx', 'cb', 'bj', 'nh', 'qp']
            area_suffix = random.choice(area_suffixes)
            simulated_id = str(random.randint(100000000, 999999999))
            return f"https://jobs.51job.com/{city_code}-{area_suffix}/{simulated_id}.html"

        else:
            # 综合爬虫，随机选择一个网站格式
            websites = ["liepin", "boss", "51job"]
            selected_site = random.choice(websites)
            return self._generate_job_detail_url(raw_data, selected_site)

    def _generate_search_fallback_url(self, raw_data: Dict[str, Any], source_website: str) -> str:
        """生成搜索链接作为备选方案（保留原有搜索逻辑）"""
        import urllib.parse

        # 获取基本信息用于URL生成
        title = raw_data.get('title', '').replace(' ', '').replace('高级', '').replace('工程师', '')
        company = raw_data.get('company', '').replace(' ', '')
        location = raw_data.get('location', '').split('-')[0]

        # 生成搜索关键词
        search_keywords = []
        if title:
            search_keywords.append(title)
        if company:
            search_keywords.append(company)

        search_query = ' '.join(search_keywords[:2])  # 限制关键词数量
        encoded_query = urllib.parse.quote(search_query)

        # 根据不同网站生成搜索URL
        if source_website == "liepin" or "liepin" in source_website.lower():
            return f"https://www.liepin.com/zhaopin/?key={encoded_query}"
        elif source_website == "boss" or "boss" in source_website.lower():
            return f"https://www.zhipin.com/web/geek/job?query={encoded_query}"
        elif source_website == "51job" or "51job" in source_website.lower():
            location_map = {
                '北京': '010000', '上海': '020000', '深圳': '040000',
                '广州': '030200', '杭州': '080200', '南京': '070200',
                '苏州': '070300', '成都': '090200', '武汉': '180200',
                '西安': '200200', '天津': '050000', '重庆': '060000'
            }
            city_code = location_map.get(location, '020000')
            return f"https://search.51job.com/list/{city_code},000000,0000,00,9,99,{encoded_query},2,1.html"
        else:
            return f"https://www.google.com/search?q={encoded_query}+招聘"

    def is_cs_related(self, job: JobPosition) -> bool:
        """判断是否为计算机科学与技术相关职位"""
        text_to_check = f"{job.title} {job.description} {job.requirements}".lower()
        
        # 检查是否包含相关关键词
        for keyword in self.cs_keywords:
            if keyword.lower() in text_to_check:
                return True
        
        return False
    
    def is_duplicate(self, job: JobPosition) -> bool:
        """检查是否为重复职位"""
        # 使用job_id检查重复
        if job.job_id in self.seen_jobs:
            self.duplicate_count += 1
            return True
        
        self.seen_jobs.add(job.job_id)
        return False
    
    def clean_job_list(self, raw_jobs: List[Dict[str, Any]], 
                      source_website: str, 
                      source_url: str) -> List[JobPosition]:
        """批量清洗职位数据"""
        cleaned_jobs = []
        
        for raw_job in raw_jobs:
            try:
                # 清洗数据
                job = self.clean_job_data(raw_job, source_website, source_url)
                
                # 检查是否为计算机相关职位
                if not self.is_cs_related(job):
                    data_logger.debug(f"跳过非计算机相关职位: {job.title}")
                    continue
                
                # 检查重复
                if self.is_duplicate(job):
                    data_logger.debug(f"跳过重复职位: {job.title} - {job.company}")
                    continue
                
                cleaned_jobs.append(job)
                
            except Exception as e:
                data_logger.error(f"处理职位数据失败: {e}")
                continue
        
        data_logger.info(f"清洗完成: 原始 {len(raw_jobs)} 条，有效 {len(cleaned_jobs)} 条，重复 {self.duplicate_count} 条")
        
        return cleaned_jobs
    
    def get_statistics(self) -> Dict[str, int]:
        """获取清洗统计信息"""
        return {
            'total_processed': len(self.seen_jobs),
            'duplicates_removed': self.duplicate_count
        }
