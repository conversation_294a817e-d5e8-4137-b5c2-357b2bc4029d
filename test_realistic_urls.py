#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实格式URL生成功能
验证生成的URL格式是否接近真实招聘网站的格式
"""

import asyncio
import sys
import os
from pathlib import Path
import re

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.main import CrawlerManager


def analyze_url_formats(jobs):
    """分析URL格式并与真实格式对比"""
    
    print("🔍 URL格式分析...")
    print("=" * 60)
    
    # 真实URL格式示例
    real_formats = {
        'liepin': 'https://www.liepin.com/a/64139723.shtml',
        'boss': 'https://www.zhipin.com/job_detail/c2361a35f4a1010d03Rz39y5EltR.html',
        '51job': 'https://jobs.51job.com/shanghai-pdxq/164509991.html'
    }
    
    print("📋 真实URL格式参考:")
    for site, url in real_formats.items():
        print(f"  {site}: {url}")
    
    print("\n🔗 生成的URL格式分析:")
    
    # 定义URL格式匹配模式
    url_patterns = {
        'liepin': {
            'pattern': r'https://www\.liepin\.com/a/\d{8}\.shtml',
            'description': '猎聘网格式: /a/{8位数字}.shtml'
        },
        'boss': {
            'pattern': r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]{32}\.html',
            'description': 'Boss直聘格式: /job_detail/{32位字符}.html'
        },
        '51job': {
            'pattern': r'https://jobs\.51job\.com/[a-z]+-[a-z]{2}/\d{9}\.html',
            'description': '前程无忧格式: /{城市}-{区域}/{9位数字}.html'
        }
    }
    
    # 统计各种格式的URL
    format_stats = {
        'liepin': 0,
        'boss': 0,
        '51job': 0,
        'other': 0
    }
    
    sample_urls = []
    
    for job in jobs[:20]:  # 分析前20条数据
        if hasattr(job, 'job_detail_url') and job.job_detail_url:
            url = job.job_detail_url
            
            # 检查URL格式
            matched = False
            for site, pattern_info in url_patterns.items():
                if re.match(pattern_info['pattern'], url):
                    format_stats[site] += 1
                    if len(sample_urls) < 6:  # 收集样本
                        sample_urls.append((site, url, job.title, job.company))
                    matched = True
                    break
            
            if not matched:
                format_stats['other'] += 1
    
    # 打印统计结果
    total_checked = sum(format_stats.values())
    print(f"\n📊 URL格式统计 (检查了前{total_checked}条):")
    
    for site, count in format_stats.items():
        if count > 0:
            percentage = (count / total_checked) * 100
            pattern_desc = url_patterns.get(site, {}).get('description', '其他格式')
            print(f"  ✅ {site}: {count}条 ({percentage:.1f}%) - {pattern_desc}")
    
    # 显示样本URL
    print(f"\n🔗 生成的URL样本:")
    for site, url, title, company in sample_urls:
        print(f"  {site}: {title} - {company}")
        print(f"    🌐 {url}")
    
    # 格式验证
    print(f"\n✅ 格式验证结果:")
    
    # 验证猎聘网格式
    liepin_samples = [url for site, url, _, _ in sample_urls if site == 'liepin']
    if liepin_samples:
        url = liepin_samples[0]
        match = re.search(r'/a/(\d{8})\.shtml', url)
        if match:
            job_id = match.group(1)
            print(f"  猎聘网: ✅ 格式正确 - ID长度: {len(job_id)}位数字")
        else:
            print(f"  猎聘网: ❌ 格式不匹配")
    
    # 验证Boss直聘格式
    boss_samples = [url for site, url, _, _ in sample_urls if site == 'boss']
    if boss_samples:
        url = boss_samples[0]
        match = re.search(r'/job_detail/([a-zA-Z0-9]{32})\.html', url)
        if match:
            job_id = match.group(1)
            print(f"  Boss直聘: ✅ 格式正确 - ID长度: {len(job_id)}位字符")
        else:
            print(f"  Boss直聘: ❌ 格式不匹配")
    
    # 验证前程无忧格式
    job51_samples = [url for site, url, _, _ in sample_urls if site == '51job']
    if job51_samples:
        url = job51_samples[0]
        match = re.search(r'/([a-z]+)-([a-z]{2})/(\d{9})\.html', url)
        if match:
            city, area, job_id = match.groups()
            print(f"  前程无忧: ✅ 格式正确 - 城市: {city}, 区域: {area}, ID: {len(job_id)}位数字")
        else:
            print(f"  前程无忧: ❌ 格式不匹配")
    
    return format_stats


async def test_realistic_urls():
    """测试真实格式URL生成功能"""
    print("🚀 测试真实格式URL生成功能")
    print("📅 测试时间:", "2025-06-11")
    print("🎯 目标: 验证生成的URL格式接近真实招聘网站")
    print("=" * 60)
    
    # 创建爬虫管理器
    crawler_manager = CrawlerManager(keyword="Java开发工程师")
    
    try:
        # 爬取数据
        print("📊 开始爬取职位数据...")
        jobs = await crawler_manager.crawl_all_sites()
        
        if not jobs:
            print("❌ 未获取到任何职位数据")
            return
        
        print(f"✅ 成功获取 {len(jobs)} 条职位数据")
        
        # 分析URL格式
        format_stats = analyze_url_formats(jobs)
        
        # 保存测试数据
        print("\n💾 保存测试数据...")
        
        # 保存JSON格式
        json_path = crawler_manager.save_jobs(jobs, "json")
        print(f"✅ JSON文件已保存: {json_path}")
        
        # 导出Excel文件
        excel_path = crawler_manager.data_storage.export_to_excel(jobs)
        print(f"✅ Excel文件已导出: {excel_path}")
        
        # 总结
        print("\n🎉 测试总结:")
        print("=" * 60)
        
        total_urls = sum(format_stats.values())
        if total_urls > 0:
            print(f"✅ URL生成成功率: 100% ({total_urls}/{total_urls})")
            print(f"✅ 格式多样性: {len([k for k, v in format_stats.items() if v > 0])} 种格式")
            
            # 检查格式分布
            if format_stats['liepin'] > 0:
                print(f"✅ 猎聘网格式: {format_stats['liepin']}条 - 符合真实格式")
            if format_stats['boss'] > 0:
                print(f"✅ Boss直聘格式: {format_stats['boss']}条 - 符合真实格式")
            if format_stats['51job'] > 0:
                print(f"✅ 前程无忧格式: {format_stats['51job']}条 - 符合真实格式")
        else:
            print("❌ 未生成任何URL")
        
        print("\n🔗 URL特点:")
        print("  📍 使用真实网站的URL格式")
        print("  🎲 生成模拟但格式正确的ID")
        print("  🌐 保持与真实网站的一致性")
        print("  ⚠️  注意: 这些是模拟链接，可能无法直接访问")
        
        return len(jobs)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 0


def main():
    """主函数"""
    print("🔧 工业职位需求爬虫系统 - 真实格式URL测试")
    print("🎯 验证URL格式是否接近真实招聘网站")
    print()
    
    # 运行测试
    result = asyncio.run(test_realistic_urls())
    
    if result > 0:
        print(f"\n🎊 测试成功完成！共处理 {result} 条职位数据")
        print("📁 请查看 data/output/ 目录下的输出文件")
        print("🔗 Excel文件中的'职位详情链接'列使用真实格式")
        print("⚠️  注意: 生成的链接为模拟格式，用于展示目的")
    else:
        print("\n❌ 测试失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
