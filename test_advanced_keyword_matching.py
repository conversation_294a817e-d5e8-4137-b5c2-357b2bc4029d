#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试深度优化的关键字匹配逻辑
验证能匹配相关职位并排除停招岗位
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.main import CrawlerManager


def test_keyword_matching_scenarios():
    """测试各种关键字匹配场景"""
    from src.data_processing.data_cleaner import AdvancedKeywordMatcher
    
    matcher = AdvancedKeywordMatcher()
    
    print("🔍 深度关键字匹配逻辑测试")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            'keyword': 'Python开发',
            'jobs': [
                {
                    'title': 'Python后端开发工程师',
                    'description': '负责Python Web开发，使用Django框架',
                    'requirements': '熟悉Python、Django、MySQL',
                    'expected': True,
                    'reason': '直接匹配Python开发'
                },
                {
                    'title': 'Java开发工程师',
                    'description': '负责Java后端开发，Spring Boot框架',
                    'requirements': '熟悉Java、Spring、MySQL',
                    'expected': False,
                    'reason': '不匹配Python，但是开发相关'
                },
                {
                    'title': 'Python数据分析师',
                    'description': '使用Python进行数据分析和机器学习',
                    'requirements': '熟悉Python、pandas、scikit-learn',
                    'expected': True,
                    'reason': '匹配Python，数据分析相关'
                },
                {
                    'title': '销售经理',
                    'description': '负责产品销售和客户维护',
                    'requirements': '有销售经验，沟通能力强',
                    'expected': False,
                    'reason': '完全不相关'
                },
                {
                    'title': 'Python开发工程师（停招）',
                    'description': '该职位已停招，暂不接受简历',
                    'requirements': '熟悉Python开发',
                    'expected': False,
                    'reason': '包含停招关键词'
                }
            ]
        },
        {
            'keyword': '机器学习',
            'jobs': [
                {
                    'title': '机器学习工程师',
                    'description': '负责机器学习算法开发和模型训练',
                    'requirements': '熟悉TensorFlow、PyTorch',
                    'expected': True,
                    'reason': '直接匹配机器学习'
                },
                {
                    'title': 'AI算法工程师',
                    'description': '开发深度学习算法，计算机视觉方向',
                    'requirements': '熟悉深度学习、CNN、RNN',
                    'expected': True,
                    'reason': '相关AI技术'
                },
                {
                    'title': '数据分析师',
                    'description': '进行业务数据分析和报表制作',
                    'requirements': '熟悉SQL、Excel、Python',
                    'expected': True,
                    'reason': '数据相关，有Python技能'
                },
                {
                    'title': '机器学习实习生（招聘已结束）',
                    'description': '机器学习相关实习工作',
                    'requirements': '在校学生，有机器学习基础',
                    'expected': False,
                    'reason': '招聘已结束'
                }
            ]
        }
    ]
    
    # 执行测试
    total_tests = 0
    passed_tests = 0
    
    for scenario in test_cases:
        keyword = scenario['keyword']
        print(f"\n🎯 测试关键词: '{keyword}'")
        print("-" * 40)
        
        for job in scenario['jobs']:
            total_tests += 1
            
            # 执行匹配
            is_relevant, score, stats = matcher.is_relevant_job(job, keyword)
            expected = job['expected']
            
            # 检查结果
            if is_relevant == expected:
                status = "✅ PASS"
                passed_tests += 1
            else:
                status = "❌ FAIL"
            
            explanation = matcher.get_match_explanation(stats, score)
            
            print(f"{status} {job['title']}")
            print(f"    预期: {'相关' if expected else '不相关'}, 实际: {'相关' if is_relevant else '不相关'}")
            print(f"    得分: {score:.1f}")
            print(f"    解释: {explanation}")
            print(f"    原因: {job['reason']}")
            print()
    
    # 总结
    print("=" * 60)
    print(f"🎉 测试完成: {passed_tests}/{total_tests} 通过 ({passed_tests/total_tests*100:.1f}%)")
    
    return passed_tests == total_tests


async def test_crawler_with_advanced_matching():
    """测试爬虫系统的高级匹配功能"""
    print("\n🚀 测试爬虫系统的高级关键字匹配")
    print("=" * 60)
    
    # 测试不同关键词
    test_keywords = [
        "Python开发工程师",
        "机器学习算法",
        "前端React开发",
        "数据分析师",
        "Java后端开发"
    ]
    
    results = {}
    
    for keyword in test_keywords:
        print(f"\n📊 测试关键词: '{keyword}'")
        print("-" * 40)
        
        try:
            # 创建爬虫管理器
            crawler_manager = CrawlerManager(keyword=keyword)
            
            # 爬取数据
            jobs = await crawler_manager.crawl_all_sites()
            
            if jobs:
                print(f"✅ 获取到 {len(jobs)} 条相关职位")
                
                # 分析匹配质量
                relevant_count = len(jobs)
                
                # 显示前3个职位作为样本
                print("📋 职位样本:")
                for i, job in enumerate(jobs[:3]):
                    print(f"  {i+1}. {job.title} - {job.company}")
                
                results[keyword] = {
                    'total_jobs': relevant_count,
                    'success': True
                }
            else:
                print("❌ 未获取到相关职位")
                results[keyword] = {
                    'total_jobs': 0,
                    'success': False
                }
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results[keyword] = {
                'total_jobs': 0,
                'success': False,
                'error': str(e)
            }
    
    # 总结结果
    print("\n" + "=" * 60)
    print("🎊 爬虫测试总结:")
    
    successful_keywords = 0
    total_jobs = 0
    
    for keyword, result in results.items():
        status = "✅" if result['success'] else "❌"
        job_count = result['total_jobs']
        
        print(f"{status} {keyword}: {job_count} 条职位")
        
        if result['success']:
            successful_keywords += 1
            total_jobs += job_count
    
    print(f"\n📈 统计:")
    print(f"  成功关键词: {successful_keywords}/{len(test_keywords)}")
    print(f"  总职位数量: {total_jobs}")
    print(f"  平均每关键词: {total_jobs/len(test_keywords):.1f} 条")
    
    return successful_keywords == len(test_keywords)


def main():
    """主函数"""
    print("🔧 工业职位需求爬虫系统 - 深度关键字匹配测试")
    print("📅 测试时间:", "2025-06-11")
    print("🎯 测试目标: 验证关键字匹配逻辑优化效果")
    print()
    
    # 测试1: 关键字匹配逻辑
    print("第一阶段: 关键字匹配逻辑测试")
    matching_success = test_keyword_matching_scenarios()
    
    # 测试2: 爬虫系统集成测试
    print("\n第二阶段: 爬虫系统集成测试")
    crawler_success = asyncio.run(test_crawler_with_advanced_matching())
    
    # 最终结果
    print("\n" + "=" * 60)
    print("🏆 最终测试结果:")
    print(f"  关键字匹配逻辑: {'✅ 通过' if matching_success else '❌ 失败'}")
    print(f"  爬虫系统集成: {'✅ 通过' if crawler_success else '❌ 失败'}")
    
    if matching_success and crawler_success:
        print("\n🎉 所有测试通过！深度关键字匹配优化成功！")
        print("✨ 优化亮点:")
        print("  - 智能识别相关职位")
        print("  - 自动排除停招岗位")
        print("  - 多维度相关性评分")
        print("  - 支持复杂关键词匹配")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步优化")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
