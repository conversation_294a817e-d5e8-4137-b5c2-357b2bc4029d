#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试深度优化的真实网址功能
验证生成的URL能够精确显示真实招聘岗位详细页面
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.main import CrawlerManager


def test_url_generation_logic():
    """测试URL生成逻辑"""
    from src.data_processing.data_cleaner import DataCleaner
    
    print("🔗 深度优化真实网址生成测试")
    print("=" * 60)
    
    cleaner = DataCleaner()
    
    # 测试用例
    test_cases = [
        {
            'raw_data': {
                'title': 'Python后端开发工程师',
                'company': '阿里巴巴集团',
                'location': '杭州-西湖区',
                'job_id': 'test001'
            },
            'website': 'liepin',
            'expected_pattern': 'liepin.com'
        },
        {
            'raw_data': {
                'title': 'Java高级开发工程师',
                'company': '腾讯科技',
                'location': '深圳-南山区',
                'job_id': 'test002'
            },
            'website': 'boss',
            'expected_pattern': 'zhipin.com'
        },
        {
            'raw_data': {
                'title': '前端React开发',
                'company': '字节跳动',
                'location': '北京-朝阳区',
                'job_id': 'test003'
            },
            'website': '51job',
            'expected_pattern': '51job.com'
        },
        {
            'raw_data': {
                'title': '机器学习算法工程师',
                'company': '百度在线',
                'location': '北京-海淀区',
                'job_id': 'test004'
            },
            'website': 'comprehensive',
            'expected_pattern': 'http'
        }
    ]
    
    print("🎯 URL生成逻辑测试:")
    print("-" * 40)
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        raw_data = case['raw_data']
        website = case['website']
        expected_pattern = case['expected_pattern']
        
        # 生成URL
        url = cleaner._generate_enhanced_job_url(raw_data, website)
        
        # 检查URL格式
        is_valid = (
            url.startswith('http') and 
            expected_pattern in url and
            len(url) > 20
        )
        
        status = "✅ PASS" if is_valid else "❌ FAIL"
        
        print(f"{status} 测试 {i}: {website} 网站")
        print(f"    职位: {raw_data['title']}")
        print(f"    公司: {raw_data['company']}")
        print(f"    生成URL: {url}")
        print(f"    URL长度: {len(url)} 字符")
        
        # 检查URL的各个部分
        if '#search_fallback=' in url:
            main_url, fallback_url = url.split('#search_fallback=')
            print(f"    主URL: {main_url}")
            print(f"    备选URL: {fallback_url}")
        
        print()
        
        if is_valid:
            passed_tests += 1
    
    print("=" * 60)
    print(f"🎉 URL生成测试完成: {passed_tests}/{total_tests} 通过 ({passed_tests/total_tests*100:.1f}%)")
    
    return passed_tests == total_tests


def test_url_stability():
    """测试URL生成的稳定性（相同输入应生成相同URL）"""
    from src.data_processing.data_cleaner import DataCleaner
    
    print("\n🔒 URL生成稳定性测试")
    print("=" * 60)
    
    cleaner = DataCleaner()
    
    test_data = {
        'title': 'Python开发工程师',
        'company': '测试公司',
        'location': '上海-浦东新区',
        'job_id': 'stability_test'
    }
    
    # 生成多次URL，检查是否一致
    urls = []
    for i in range(5):
        url = cleaner._generate_enhanced_job_url(test_data, 'liepin')
        urls.append(url)
    
    # 检查稳定性
    all_same = all(url == urls[0] for url in urls)
    
    print(f"🎯 稳定性测试结果: {'✅ 通过' if all_same else '❌ 失败'}")
    print(f"生成的URL: {urls[0]}")
    print(f"重复生成 {len(urls)} 次，结果{'一致' if all_same else '不一致'}")
    
    return all_same


async def test_crawler_url_output():
    """测试爬虫系统的URL输出功能"""
    print("\n🚀 爬虫系统URL输出测试")
    print("=" * 60)
    
    # 测试关键词
    test_keyword = "Python开发工程师"
    
    print(f"📊 测试关键词: '{test_keyword}'")
    print("-" * 40)
    
    try:
        # 创建爬虫管理器
        crawler_manager = CrawlerManager(keyword=test_keyword)
        
        # 爬取少量数据进行测试
        jobs = await crawler_manager.crawl_all_sites()
        
        if jobs:
            print(f"✅ 获取到 {len(jobs)} 条职位数据")
            
            # 检查URL质量
            valid_urls = 0
            total_urls = 0
            
            print("\n📋 URL质量检查:")
            for i, job in enumerate(jobs[:5]):  # 检查前5个职位
                total_urls += 1
                url = job.job_detail_url
                
                # 检查URL有效性
                is_valid = (
                    url and 
                    url.startswith('http') and 
                    len(url) > 20 and
                    any(domain in url for domain in ['liepin.com', 'zhipin.com', '51job.com'])
                )
                
                if is_valid:
                    valid_urls += 1
                
                status = "✅" if is_valid else "❌"
                print(f"  {status} {job.title} - {job.company}")
                print(f"      URL: {url}")
                print(f"      长度: {len(url)} 字符")
                
                # 显示URL结构
                if '#search_fallback=' in url:
                    main_url, fallback_url = url.split('#search_fallback=')
                    print(f"      主URL: {main_url}")
                    print(f"      备选: {fallback_url[:50]}...")
                print()
            
            # 统计结果
            url_quality = valid_urls / total_urls if total_urls > 0 else 0
            print(f"📈 URL质量统计:")
            print(f"  有效URL: {valid_urls}/{total_urls} ({url_quality:.1%})")
            
            # 测试数据导出
            print(f"\n💾 测试数据导出:")
            try:
                from src.data_processing.data_storage import DataStorage
                storage = DataStorage()
                
                # 导出CSV
                csv_file = storage._save_as_csv(jobs)
                print(f"  ✅ CSV导出成功: {csv_file}")
                
                # 检查CSV文件内容
                import pandas as pd
                df = pd.read_csv(csv_file)
                if '真实网址' in df.columns:
                    print(f"  ✅ CSV包含'真实网址'列")
                    non_empty_urls = df['真实网址'].notna().sum()
                    print(f"  📊 有效URL数量: {non_empty_urls}/{len(df)}")
                else:
                    print(f"  ❌ CSV缺少'真实网址'列")
                
                return url_quality >= 0.8  # 80%以上的URL有效
                
            except Exception as e:
                print(f"  ❌ 数据导出测试失败: {e}")
                return False
        else:
            print("❌ 未获取到职位数据")
            return False
            
    except Exception as e:
        print(f"❌ 爬虫测试失败: {e}")
        return False


def test_url_accessibility():
    """测试生成的URL可访问性（模拟测试）"""
    print("\n🌐 URL可访问性模拟测试")
    print("=" * 60)
    
    # 模拟不同类型的URL
    test_urls = [
        "https://www.liepin.com/a/64139723.shtml",
        "https://www.zhipin.com/job_detail/c2361a35f4a1010d03Rz39y5EltR.html",
        "https://jobs.51job.com/shanghai-pd/164509991.html"
    ]
    
    print("🎯 URL格式验证:")
    
    valid_count = 0
    for i, url in enumerate(test_urls, 1):
        # 检查URL格式
        is_valid_format = (
            url.startswith('https://') and
            len(url) > 30 and
            any(domain in url for domain in ['liepin.com', 'zhipin.com', '51job.com'])
        )
        
        status = "✅ 格式正确" if is_valid_format else "❌ 格式错误"
        print(f"  {i}. {status}: {url}")
        
        if is_valid_format:
            valid_count += 1
    
    print(f"\n📊 格式验证结果: {valid_count}/{len(test_urls)} 通过")
    
    return valid_count == len(test_urls)


def main():
    """主函数"""
    print("🔧 工业职位需求爬虫系统 - 真实网址深度优化测试")
    print("📅 测试时间:", "2025-06-11")
    print("🎯 测试目标: 验证真实网址生成和输出功能")
    print()
    
    # 测试1: URL生成逻辑
    print("第一阶段: URL生成逻辑测试")
    url_generation_success = test_url_generation_logic()
    
    # 测试2: URL稳定性
    print("\n第二阶段: URL稳定性测试")
    url_stability_success = test_url_stability()
    
    # 测试3: 爬虫系统集成测试
    print("\n第三阶段: 爬虫系统集成测试")
    crawler_success = asyncio.run(test_crawler_url_output())
    
    # 测试4: URL可访问性
    print("\n第四阶段: URL可访问性测试")
    accessibility_success = test_url_accessibility()
    
    # 最终结果
    print("\n" + "=" * 60)
    print("🏆 最终测试结果:")
    print(f"  URL生成逻辑: {'✅ 通过' if url_generation_success else '❌ 失败'}")
    print(f"  URL稳定性: {'✅ 通过' if url_stability_success else '❌ 失败'}")
    print(f"  爬虫系统集成: {'✅ 通过' if crawler_success else '❌ 失败'}")
    print(f"  URL可访问性: {'✅ 通过' if accessibility_success else '❌ 失败'}")
    
    all_passed = all([url_generation_success, url_stability_success, crawler_success, accessibility_success])
    
    if all_passed:
        print("\n🎉 所有测试通过！真实网址深度优化成功！")
        print("✨ 优化亮点:")
        print("  - 生成高质量的真实格式URL")
        print("  - 支持多个主流招聘网站格式")
        print("  - 提供主URL和备选搜索URL")
        print("  - 确保URL生成的稳定性")
        print("  - CSV输出包含'真实网址'列")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步优化")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
